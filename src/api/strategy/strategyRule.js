import request from '@/utils/HttpUtils'

const baseUri = 'o/strategy/rule/'

export function pageStrategyRule(data) {
  return request({
    url: baseUri + 'page',
    method: 'post',
    data: data
  })
}

export function addStrategyRule(data) {
  return request({
    url: baseUri + 'add',
    method: 'post',
    data: data
  })
}

export function modifyStrategyRule(data) {
  return request({
    url: baseUri + 'modify',
    method: 'post',
    data: data
  })
}

export function onOffStrategyRule(data) {
  return request({
    url: baseUri + 'onOff/' + data.id + '/' + data.state,
    method: 'post'
  })
}

export function infoStrategyRule(data) {
  return request({
    url: baseUri + 'info',
    method: 'post',
    data: data
  })
}

export function deleteStrategyRule(data) {
  return request({
    url: baseUri + 'delete',
    method: 'post',
    data: data
  })
}

export function strategyRuleTrendApi(data) {
  return request({
    url: baseUri + 'trend',
    method: 'post',
    data: data
  })
}

export function batchAddStrategyRule(data) {
  return request({
    url: baseUri + 'batchAdd',
    method: 'post',
    data: data
  })
}
