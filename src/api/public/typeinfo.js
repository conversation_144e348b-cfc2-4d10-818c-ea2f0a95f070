import request from '@/utils/HttpUtils'

const baseUri = 'p/typeInfo/'

// 媒体协议类型
export function listMediaProtocolTypeApi() {
  return request({
    url: baseUri + 'mediaProtocolType',
    method: 'post'
  })
}

// 系统类型
export function listOsTypeApi() {
  return request({
    url: baseUri + 'osType',
    method: 'post'
  })
}

// 广告位类型
export function listTagTypeApi() {
  return request({
    url: baseUri + 'tagType',
    method: 'post'
  })
}

// 广告位流量类型
export function listFlowTypeApi() {
  return request({
    url: baseUri + 'flowType',
    method: 'post'
  })
}

// 结算方式
export function listBidTypeApi() {
  return request({
    url: baseUri + 'bidType',
    method: 'post'
  })
}

// 事件列表
export function listEventApi() {
  return request({
    url: baseUri + 'event',
    method: 'post'
  })
}

export function listConnectTypeApi() {
  return request({
    url: baseUri + 'connectType',
    method: 'post'
  })
}

export function listCarrierTypeApi() {
  return request({
    url: baseUri + 'carrierType',
    method: 'post'
  })
}

export function listDeviceBrandApi() {
  return request({
    url: baseUri + 'deviceBrand',
    method: 'post'
  })
}

export function listLimitTypeApi() {
  return request({
    url: baseUri + 'limitType',
    method: 'post'
  })
}

export function listBidPriceTypeApi() {
  return request({
    url: baseUri + 'bidPriceType',
    method: 'post'
  })
}

export function listSharingPriceTypeApi() {
  return request({
    url: baseUri + 'sharingPriceType',
    method: 'post'
  })
}

export function listCompareTypeApi() {
  return request({
    url: baseUri + 'compareType',
    method: 'post'
  })
}

export function actionTypeApi() {
  return request({
    url: baseUri + 'actionType',
    method: 'post'
  })
}

export function listQuantityLimitTypeApi() {
  return request({
    url: baseUri + 'quantityLimitType',
    method: 'post'
  })
}

export function listParallelPriorityApi() {
  return request({
    url: baseUri + 'parallelPriority',
    method: 'post'
  })
}

export function listPackageHandleTypeApi() {
  return request({
    url: baseUri + 'packageHandleType',
    method: 'post'
  })
}

export function financialStateApi() {
  return request({
    url: baseUri + 'financialState',
    method: 'post'
  })
}

export function financialReleaseStateApi() {
  return request({
    url: baseUri + 'financialReleaseState',
    method: 'post'
  })
}

export function systemTaskTypeApi() {
  return request({
    url: baseUri + 'systemTaskType',
    method: 'post'
  })
}

export function systemTaskStateApi() {
  return request({
    url: baseUri + 'systemTaskState',
    method: 'post'
  })
}

export function materialTypeApi() {
  return request({
    url: baseUri + 'materialType',
    method: 'post'
  })
}

export function closeAdRuleTypeApi() {
  return request({
    url: baseUri + 'closeAdRuleType',
    method: 'post'
  })
}

export function conversionTypeApi() {
  return request({
    url: baseUri + 'conversionType',
    method: 'post'
  })
}
export function conversionStatusApi() {
  return request({
    url: baseUri + 'conversionStatus',
    method: 'post'
  })
}
