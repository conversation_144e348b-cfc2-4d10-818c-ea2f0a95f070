#!/bin/bash
# stop ssp-rtb-service
#########################

# 定义相关变量
SERVICE_NAME="ssp-rtb-service"
SERVICE_DIR="/opt/ssp/app"
PID_FILE="${SERVICE_DIR}/${SERVICE_NAME}.pid"

# 信号处理
trap "rm -rf $PID_FILE; exit" INT TERM EXIT

# 检测PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo -e "\033[32mThe service is not running.\033[0m"
    exit 1
fi

# 获取进程PID
pid=$(cat "$PID_FILE")

# 重新加载环境变量
source /etc/profile

# 停止进程
kill "$pid" > /dev/null 2>&1

# 检查进程是否停止
for i in {1..120}
do
  if ps $pid > /dev/null 2>&1 ;then
    echo "wait stop $SERVICE_NAME ..."
    sleep 1
  else
     echo -e "\033[32mThe service has stopped successfully..\033[0m"
     rm -rf $PID_FILE
     break
  fi
done

# 清除trap
trap - INT TERM EXIT

