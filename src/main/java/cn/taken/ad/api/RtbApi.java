package cn.taken.ad.api;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.utils.date.DateUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.components.StrategyComponent;
import cn.taken.ad.configuration.PropertiesConfiguration;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.configuration.counter.RtbCounter;
import cn.taken.ad.configuration.log.RtbOpenLogHandler;
import cn.taken.ad.configuration.monitor.RtbMonitor;
import cn.taken.ad.configuration.server.ServerInfoManager;
import cn.taken.ad.constant.business.BidPriceType;
import cn.taken.ad.constant.business.BidType;
import cn.taken.ad.constant.business.CompareType;
import cn.taken.ad.constant.business.DeviceBrand;
import cn.taken.ad.constant.business.EventType;
import cn.taken.ad.constant.business.MacroType;
import cn.taken.ad.constant.business.OsType;
import cn.taken.ad.constant.business.QuantityLimitType;
import cn.taken.ad.constant.business.SharingPriceType;
import cn.taken.ad.constant.business.TagType;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.constant.state.BaseState;
import cn.taken.ad.core.pojo.advertiser.AdvertiserApp;
import cn.taken.ad.core.pojo.advertiser.AdvertiserTag;
import cn.taken.ad.core.pojo.base.BaseCity;
import cn.taken.ad.core.pojo.base.BaseProvince;
import cn.taken.ad.core.pojo.dmp.DmpPackage;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaProtocol;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.core.pojo.strategy.Strategy;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.logic.MediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestGeoDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestInstalledAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseTrackDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.util.ChinaGeoFenceUtil;
import cn.taken.ad.utils.DmpDeviceCacheUtils;
import cn.taken.ad.utils.ExpireUtils;
import cn.taken.ad.utils.TrackUtil;
import cn.taken.ad.utils.application.ApplicationContextUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import net.dreamlu.mica.ip2region.core.IpInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
public class RtbApi {

    private static final Logger log = LoggerFactory.getLogger(RtbApi.class);

    @Resource(name = "BaseRedisL2Cache")
    private BaseRedisL2Cache baseRedisL2Cache;

    @Resource(name = "BaseRedis")
    private RedisClient redis;
    @Resource
    private PropertiesConfiguration propertiesConfiguration;
    @Resource
    private RtbMonitor rtbMonitor;
    @Resource
    private RtbOpenLogHandler rtbOpenLogService;
    @Resource
    private Ip2regionSearcher ip2regionSearcher;
    @Resource
    private RtbCounter rtbCounter;
    @Resource(name = "ServerInfoManager")
    private ServerInfoManager serverInfoManager;

    private static final Pattern MODEL = Pattern.compile("^iPhone\\d{1,2},\\d$");

    @RequestMapping(value = "/rtb/{mediaCode}")
    public void rtb(@PathVariable String mediaCode) {
        long recvTime = System.currentTimeMillis();
        // 唯一标识流量主请求
        String rtbId = serverInfoManager.genOnlyBusinessId();
        RtbDto rtbDto = new RtbDto(recvTime, rtbId);
        rtbDto.setMediaDto(new RtbMediaDto());
        RtbResponseDto errorResp = null;
        LinkedList<StrategyTagAdvertiser> advertisers = null;
        Media media = null;
        Strategy strategy = null;
        boolean mediaRespFail = false;
        MediaTag mediaTag = null;
        MediaProcessor processor = null;

        // 是否无效请求 媒体请求参数校验完成后 修改为false
        boolean isInvalid = true;
        try {
            media = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_CODE_ + mediaCode, Media.class);
            if (media == null) {
                errorResp = new RtbResponseDto(LogicState.ERROR_NOT_FOUND_MEDIA.getCode(), LogicState.ERROR_NOT_FOUND_MEDIA.getDesc() + ":" + mediaCode);
                return;
            }
            rtbDto.getMediaDto().setMediaId(media.getId());
            rtbDto.getMediaDto().setMediaCode(media.getCode());
            rtbDto.getMediaDto().setMediaPnyParam(media.getPnyParam());
            rtbDto.getMediaDto().setMediaPriceKey(media.getPriceKey());
            MediaProtocol protocol = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_PROTOCOL_ID_ + media.getProtocolId(), MediaProtocol.class);
            if (protocol == null) {
                errorResp = new RtbResponseDto(LogicState.ERROR_NOT_FOUND_MEDIA_PROTOCOL.getCode(), LogicState.ERROR_NOT_FOUND_MEDIA_PROTOCOL.getDesc() + ":" + media.getProtocolId());
                return;
            }
            rtbDto.getMediaDto().setMediaProtocolId(protocol.getId());
            rtbDto.getMediaDto().setMediaProtocolIsNeedAppCode(protocol.getIsNeedAppCode());
            processor = ApplicationContextUtils.getBean(protocol.getCode().toUpperCase() + LogicSuffix.MEDIA_LOGIC_SUFFIX, MediaProcessor.class);
            //转化请求
            RtbRequestDto request = processor.parseRtb(rtbDto.getMediaDto(), rtbId);
            SuperResult<Object> checkRes = checkParam(media, request);
            if (!checkRes.getSuccess()) {
                errorResp = new RtbResponseDto(checkRes.getCode(), checkRes.getMessage());
                return;
            }
            rtbDto.getMediaDto().setRequest(request);
            MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_CODE_ + media.getId() + "_" + request.getApp().getAppId(), MediaApp.class);
            rtbDto.getMediaDto().setMediaAppId(mediaApp.getId());
            rtbDto.getMediaDto().setMediaAppCode(mediaApp.getCode());
            rtbDto.getMediaDto().setMediaAppPnyParam(mediaApp.getPnyParam());

            mediaTag = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_TAG_CODE_ + media.getId() + "_" + mediaApp.getId() + "_" + request.getTag().getTagId(), MediaTag.class);
            rtbDto.getMediaDto().setMediaTagId(mediaTag.getId());
            rtbDto.getMediaDto().setMediaTagCode(mediaTag.getCode());
            rtbDto.getMediaDto().setMediaTagType(TagType.findByType(mediaTag.getType()));
            rtbDto.getMediaDto().setMediaTagPnyParam(mediaTag.getPnyParam());
            //是否无效请求 校验完成后赋值
            isInvalid = false;
            //查找策略
            strategy = baseRedisL2Cache.get(BaseRedisKeys.KV_STRATEGY_MEDIA_TAG_ID_ + mediaTag.getId(), Strategy.class);
            if (strategy == null || strategy.getState() == BaseState.OFF.getState()) {
                errorResp = new RtbResponseDto(LogicState.ERROR_NOT_FOUND_MEDIA_STRATEGY.getCode(), LogicState.ERROR_NOT_FOUND_MEDIA_STRATEGY.getDesc() + ":" + mediaTag.getId());
                return;
            }
            rtbDto.setStrategyTagId(strategy.getId());
            Long[] advIds = baseRedisL2Cache.get(BaseRedisKeys.KV_STRATEGY_MEDIA_TAG_ADVERTISER_TAGS_ID_ + strategy.getId(), Long[].class);
            if (advIds == null || advIds.length == 0) {
                errorResp = new RtbResponseDto(LogicState.ERROR_NOT_FOUND_ADVERTISER.getCode(), LogicState.ERROR_NOT_FOUND_ADVERTISER.getDesc() + ":" + strategy.getId());
                return;
            }
            advertisers = new LinkedList<>();
            SuperResult<String> filterRes = null;
            for (Long advId : advIds) {
                StrategyTagAdvertiser advertiser = baseRedisL2Cache.get(BaseRedisKeys.KV_STRATEGY_ADVERTISER_TAG_ID_ + advId, StrategyTagAdvertiser.class);
                if (advertiser != null && advertiser.getState() == BaseState.ON.getState() && advertiser.getIsDelete() == 0) {
                    filterRes = checkFilter(mediaTag, request, advertiser);
                    if (filterRes.getSuccess()) {
                        advertisers.add(advertiser);
                    }
                }
            }
            if (advertisers.isEmpty() && filterRes != null) {
                errorResp = new RtbResponseDto(filterRes.getCode(), filterRes.getMessage());
                return;
            }
            int type = strategy.getQuantityLimitType();
            if (QuantityLimitType.RANDOM.getType() == type) { //随机的
                if (advertisers.size() > 1) {
                    StrategyTagAdvertiser next = getRandomAdvertiserIndex(advertisers);
                    advertisers.clear();
                    if (null != next) {
                        advertisers.add(next);
                    }//为空时 全都未配置权重,不请求
                }
            } else {//并行-串行
                //高优先级排前面
                advertisers.sort(Comparator.comparing(StrategyTagAdvertiser::getParallelPriority).reversed());
                int size = advertisers.size();
                while (size > strategy.getParallelMaxAdv()) {
                    advertisers.removeLast();
                    size = advertisers.size();
                }
            }
            if (advertisers.isEmpty()) {
                errorResp = new RtbResponseDto(LogicState.ERROR_NOT_FOUND_ADVERTISER.getCode(), LogicState.ERROR_NOT_FOUND_ADVERTISER.getDesc() + ":" + strategy.getId());
                return;
            }
            String strategyBeanName = advertisers.size() == 1 ? LogicSuffix.STRATEGY_LOGIC_PREFIX + 1 : LogicSuffix.STRATEGY_LOGIC_PREFIX + type;
            StrategyComponent strategyComponent = ApplicationContextUtils.getBean(strategyBeanName, StrategyComponent.class);
            SuperResult<RtbAdvDto> result = strategyComponent.strategyDispatcher(request, rtbDto, mediaTag, advertisers);
            if (!result.getSuccess()) {
                errorResp = new RtbResponseDto(result.getCode(), result.getMessage());
                return;
            }
            RtbAdvDto rtbAdvDto = result.getResult();
            List<TagResponseDto> tags = rtbAdvDto.getRtbResponseDto().getTags();
            if (tags == null || tags.isEmpty()) {
                errorResp = new RtbResponseDto(LogicState.SUCCESS_NON_PARTICIPATION.getCode(), LogicState.SUCCESS_NON_PARTICIPATION.getDesc());
                return;
            }
            rtbDto.getMediaDto().setResponse(rtbAdvDto.getRtbResponseDto());
            rtbAdvDto.getRtbResponseDto().setRespId(rtbId);
            for (TagResponseDto item : rtbAdvDto.getRtbResponseDto().getTags()) {
                // 创建缓存实例
                RtbBillDto cacheDto = new RtbBillDto();
                cacheDto.setRtbId(rtbId);
                cacheDto.setAdvPrice(item.getPrice());
                cacheDto.setMediaId(media.getId());
                cacheDto.setMediaAppId(mediaTag.getMediaAppId());
                cacheDto.setMediaTagId(mediaTag.getId());
                cacheDto.setStrategyId(strategy.getId());
                cacheDto.setRespMediaPrice(item.getRespMediaPrice());
                cacheDto.setStrategyTagAdvId(rtbAdvDto.getStrategyTagAdvId());
                cacheDto.setAdvertiserId(rtbAdvDto.getAdvertiserId());
                cacheDto.setAdvertiserAppId(rtbAdvDto.getAppId());
                cacheDto.setAdvertiserTagId(rtbAdvDto.getTagId());
                cacheDto.setCreativeId(rtbAdvDto.getCreativeId());
                cacheDto.setAdvRespId(item.getTagInfoId());
                insertWinNoticeUrl(cacheDto, item, mediaTag.getNeedHttps() != null && mediaTag.getNeedHttps() == 1);
                RtbEventDto eventDto = new RtbEventDto();
                eventDto.setRtbId(rtbId);
                eventDto.setAdvPrice(item.getPrice());
                eventDto.setRespMediaPrice(item.getRespMediaPrice());
                eventDto.setAdvertiserId(rtbAdvDto.getAdvertiserId());
                eventDto.setAdvertiserAppId(rtbAdvDto.getAppId());
                eventDto.setAdvertiserTagId(rtbAdvDto.getTagId());
                eventDto.setStrategyId(strategy.getId());
                eventDto.setStrategyTagAdvId(rtbAdvDto.getStrategyTagAdvId());
                eventDto.setMediaAppId(mediaTag.getMediaAppId());
                eventDto.setMediaId(media.getId());
                eventDto.setMediaTagId(mediaTag.getId());
                eventDto.setCreativeId(rtbAdvDto.getCreativeId());
                eventDto.setDeviceIds(request.getDevice().toDeviceMd5List());
                insertTrack(eventDto, item, mediaTag.getNeedHttps() != null && mediaTag.getNeedHttps() == 1, rtbAdvDto.getTagId());
            }
        } catch (Throwable e) {
            log.error("meida code:{} error", mediaCode, e);
            errorResp = new RtbResponseDto(LogicState.ERROR_REQUEST_PARAM.getCode(), LogicState.ERROR_REQUEST_PARAM.getDesc());
        } finally {
            String code = "";
            String desc = "";
            if (errorResp != null && Objects.equals(errorResp.getCode(), LogicState.ERROR_NOT_FOUND_MEDIA.getCode())) {
                //未查询到媒体 httpCode 返回404
                HttpResponseUtils.getCurrentHttpResponse().setStatus(404);
                code = errorResp.getCode();
                desc = errorResp.getMsg();
            } else {
                if (rtbDto.getMediaDto().getResponse() == null) {
                    rtbDto.getMediaDto().setResponse(errorResp);
                }
                if (null == rtbDto.getMediaDto().getResponse().getRespId()) {
                    rtbDto.getMediaDto().getResponse().setRespId(rtbId);
                }
                LogicState state = LogicState.findByCode(rtbDto.getMediaDto().getResponse().getCode());
                code = state.getCode();
                desc = rtbDto.getMediaDto().getResponse().getMsg();
                if (null != processor) {
                    try {
                        SuperResult<String> res = processor.returnRtb(rtbDto.getMediaDto());
                        mediaRespFail = !res.getSuccess();
                    } catch (Throwable e) {
                        mediaRespFail = true;
                        if (e instanceof IOException && (e.getMessage().contains("Broken pipe") || e.getMessage().contains("Connection reset by peer"))) {
                            log.info("return media:{},tagId:{} resp timeout", rtbDto.getMediaDto().getMediaCode(), rtbDto.getMediaDto().getMediaTagCode());
                        } else {
                            log.error("return media:{},tagId:{} resp error", rtbDto.getMediaDto().getMediaCode(), rtbDto.getMediaDto().getMediaTagCode(), e);
                        }
                    }
                }
                int useTime = (int) (System.currentTimeMillis() - recvTime);
                //统计
                processMonitor(mediaTag, advertisers, rtbDto, null == strategy ? 0L : strategy.getId(), state.getCode(), mediaRespFail, isInvalid, useTime);
                //日志收集
                rtbOpenLogService.collectLog(rtbDto);
            }
            if (!code.equals(LogicState.SUCCESS.getCode()) && !code.equals(LogicState.SUCCESS_NON_PARTICIPATION.getCode())) {
                log.info("RtbId:{},rtb mediaCode:{},appCode:{},tagCode:{},code:{},msg:{}",rtbId, mediaCode, rtbDto.getMediaDto().getMediaAppCode(), rtbDto.getMediaDto().getMediaTagCode(), code, desc);
            }
        }
    }

    /**
     * 添加平台自己的监控事件,将数据压缩加密 转码后添加到各事件链接中
     */
    private void insertTrack(RtbEventDto eventDto, TagResponseDto tagInfo, boolean needHttps, Long advertiserTagId) {
        // 压缩转码
        String param = TrackUtil.compressParam(JsonHelper.toJsonStringWithoutNull(eventDto));
        // 循环所有监控事件
        // tracks 按照监控类型分组
        Map<Integer, List<ResponseTrackDto>> eventMap = tagInfo.getTracks().stream().collect(Collectors.groupingBy(ResponseTrackDto::getTrackType));
        // 存储最终的监控事件
        List<ResponseTrackDto> finalTracks = new ArrayList<>();
        eventMap.forEach((type, tracks) -> {
            // 分离 GET 和 POST 请求
            Map<String, List<ResponseTrackDto>> methodMap = tracks.stream()
                    .collect(Collectors.groupingBy(track -> StringUtils.isNotBlank(track.getMethod()) ? track.getMethod().toUpperCase() : HttpMethod.GET.name().toUpperCase()));

            // 平台自己的链接
            String platformUrl = (needHttps ? "https://" : "http://") + propertiesConfiguration.getTrackDomain() + LogicSuffix.EVENT_URL_LOGIC_SUFFIX + type + "?s=" + param + (Objects.equals(type, EventType.EXPOSURE.getType()) ? "&price=" + MacroType.WIN_PRICE.getCode() : "");

            // 处理 GET 请求 - 合并URLs
            List<ResponseTrackDto> getTracks = methodMap.get(HttpMethod.GET.name().toUpperCase());
            boolean addPostUrl = getTracks == null || getTracks.isEmpty();
            if (getTracks != null && !getTracks.isEmpty()) {
                List<String> allGetUrls = getTracks.stream()
                        .flatMap(track -> track.getTrackUrls().stream())
                        .collect(Collectors.toList());
                allGetUrls.add(platformUrl);
                ResponseTrackDto mergedGet = new ResponseTrackDto(type, allGetUrls);
                mergedGet.setMethod(HttpMethod.GET.name().toUpperCase());
                finalTracks.add(mergedGet);
            }
            // 处理 POST 请求 - 保持原样
            List<ResponseTrackDto> postTracks = methodMap.get(HttpMethod.POST.name().toUpperCase());
            if (postTracks != null && !postTracks.isEmpty()) {
                for (ResponseTrackDto item : postTracks) {
                    item.setMethod(HttpMethod.POST.name().toUpperCase());
                    finalTracks.add(item);
                    if (addPostUrl) {
                        // GET已经包含改类型事件，POST不再追加
                        finalTracks.add(new ResponseTrackDto(type, new ArrayList<>(Collections.singletonList(platformUrl)), HttpMethod.POST.name().toUpperCase(), item.getContentType(), item.getContent()));
                    }
                }
            }
        });
        // 覆盖原链接
        tagInfo.setTracks(finalTracks);
        // 判断预算广告位追加的事件
        List<Integer> addEventTypes = new ArrayList<>();
        AdvertiserTag tag = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_TAG_ID_ + advertiserTagId, AdvertiserTag.class);
        if (null != tag && StringUtils.isNotBlank(tag.getAddEventTypes())) {
            addEventTypes = JsonHelper.fromJson(new TypeToken<List<Integer>>() {
            }, tag.getAddEventTypes());
        }
        if (addEventTypes != null && !addEventTypes.isEmpty()) {
            for (Integer eventType : addEventTypes) {
                if (!eventMap.containsKey(eventType)) {
                    String url = (needHttps ? "https://" : "http://") + propertiesConfiguration.getTrackDomain() + LogicSuffix.EVENT_URL_LOGIC_SUFFIX + eventType + "?s=" + param;
                    if (Objects.equals(eventType, EventType.EXPOSURE.getType())) {
                        url += "&price=" + MacroType.WIN_PRICE.getCode();
                    }
                    ResponseTrackDto trackDto = new ResponseTrackDto(eventType, new ArrayList<>(Collections.singletonList(url)));
                    trackDto.setMethod(HttpMethod.GET.name().toUpperCase());
                    tagInfo.getTracks().add(trackDto);
                }
            }
        } else {
            if (!eventMap.containsKey(EventType.CLICK.getType())) {
                // 必须添加的 点击监控
                String url = (needHttps ? "https://" : "http://") + propertiesConfiguration.getTrackDomain() + LogicSuffix.EVENT_URL_LOGIC_SUFFIX + EventType.CLICK.getType() + "?s=" + param;
                ResponseTrackDto trackDto = new ResponseTrackDto(EventType.CLICK.getType(), new ArrayList<>(Collections.singletonList(url)));
                trackDto.setMethod(HttpMethod.GET.name().toUpperCase());
                tagInfo.getTracks().add(trackDto);
            }
            if (!eventMap.containsKey(EventType.EXPOSURE.getType())) {
                // 必须添加的 曝光监控
                String url = (needHttps ? "https://" : "http://") + propertiesConfiguration.getTrackDomain() + LogicSuffix.EVENT_URL_LOGIC_SUFFIX + EventType.EXPOSURE.getType() + "?s=" + param + "&price=" + MacroType.WIN_PRICE.getCode();
                ResponseTrackDto trackDto = new ResponseTrackDto(EventType.EXPOSURE.getType(), new ArrayList<>(Collections.singletonList(url)));
                trackDto.setMethod(HttpMethod.GET.name().toUpperCase());
                tagInfo.getTracks().add(trackDto);

            }
        }

    }

    private void insertWinNoticeUrl(RtbBillDto dto, TagResponseDto tagInfo, boolean https) {
        // 压缩转码 放到url中
        List<String> advWinUrl = tagInfo.getWinNoticeUrls();
        List<String> advFailUrl = tagInfo.getFailNoticeUrls();
        if (null != advWinUrl && !advWinUrl.isEmpty()) {
            dto.setAdvUrls(advWinUrl);
        }
        String param = TrackUtil.compressParam(JsonHelper.toJsonString(dto));
        String winUrl = genWinNoticeUrl(param, https);
        tagInfo.setWinNoticeUrls(Collections.singletonList(winUrl));
        StringBuilder append = new StringBuilder();
        if (null != advFailUrl && !advFailUrl.isEmpty()) {
            dto.setAdvUrls(advFailUrl);
            //判断responseDto中的竟败链接中是否包含如下宏(快手的宏)，如果包含，平台自己的竟败链接需要追加宏参数
            for (int i = 0; i < dto.getAdvUrls().size(); i++) {
                String url = dto.getAdvUrls().get(i);
                if (url.contains(MacroType.ADNTYPE.getCode()) && append.indexOf(MacroType.ADNTYPE.getCode()) == -1) {
                    append.append("&").append(MacroType.ADNTYPE.name().toLowerCase()).append("=").append(MacroType.ADNTYPE.getCode());
                }
                if (url.contains(MacroType.ADNNAME.getCode()) && append.indexOf(MacroType.ADNNAME.getCode()) == -1) {
                    append.append("&").append(MacroType.ADNNAME.name().toLowerCase()).append("=").append(MacroType.ADNNAME.getCode());
                }
                if (url.contains(MacroType.ADN.getCode()) && append.indexOf(MacroType.ADN.getCode()) == -1) {
                    append.append("&").append(MacroType.ADN.name().toLowerCase()).append("=").append(MacroType.ADN.getCode());
                }
                if (url.contains(MacroType.ADTI.getCode()) && append.indexOf(MacroType.ADTI.getCode()) == -1) {
                    append.append("&").append(MacroType.ADTI.name().toLowerCase()).append("=").append(MacroType.ADTI.getCode());
                }
                if (url.contains(MacroType.ADREQID.getCode()) && append.indexOf(MacroType.ADREQID.getCode()) == -1) {
                    append.append("&").append(MacroType.ADREQID.name().toLowerCase()).append("=").append(MacroType.ADREQID.getCode());
                }
                if (url.contains(MacroType.ISS.getCode()) && append.indexOf(MacroType.ISS.getCode()) == -1) {
                    append.append("&").append(MacroType.ISS.name().toLowerCase()).append("=").append(MacroType.ISS.getCode());
                }
                if (url.contains(MacroType.ISC.getCode()) && append.indexOf(MacroType.ISC.getCode()) == -1) {
                    append.append("&").append(MacroType.ISC.name().toLowerCase()).append("=").append(MacroType.ISC.getCode());
                }
            }
        }
        param = TrackUtil.compressParam(JsonHelper.toJsonString(dto));
        String failUrl = genFailNoticeUrl(param, https, append.length() == 0 ? null : append.toString());
        tagInfo.setFailNoticeUrls(Collections.singletonList(failUrl));
    }

    /**
     * 生成 竟胜链接
     */
    private String genWinNoticeUrl(String param, boolean https) {
        String protocol = https ? "https://" : "http://";
        return protocol + propertiesConfiguration.getNoticeDomain() + LogicSuffix.BILL_WIN_LOGIC_SUFFIX + "?s=" + param + "&price=" + MacroType.WIN_PRICE.getCode() + "&lossRp=" + MacroType.LOSS_RP.getCode();
    }

    private String genFailNoticeUrl(String param, boolean https, String append) {
        String protocol = https ? "https://" : "http://";
        return protocol + propertiesConfiguration.getNoticeDomain() + LogicSuffix.BILL_FAIL_LOGIC_SUFFIX + "?s=" + param + "&price=" + MacroType.WINNER_PRICE.getCode() + (append != null ? append : "");
    }

    private StrategyTagAdvertiser getRandomAdvertiserIndex(List<StrategyTagAdvertiser> list) {
        // 计算总权重
        int totalWeight = 0;
        for (StrategyTagAdvertiser adv : list) {
            totalWeight += adv.getRandomFlowRatio();
        }
        // 全都未分配权重
        if (totalWeight <= 0) {
            return null;
        }
        // 生成一个随机权重值
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        // 根据随机权重值选择广告主
        int cumulativeWeight = 0;
        for (StrategyTagAdvertiser strategyTagAdvertiser : list) {
            cumulativeWeight += strategyTagAdvertiser.getRandomFlowRatio();
            if (randomWeight < cumulativeWeight) {
                return strategyTagAdvertiser;
            }
        }
        // 默认 返回最后一个
        return list.get(list.size() - 1);
    }

    private void processMonitor(MediaTag mediaTag, List<StrategyTagAdvertiser> advertisers, RtbDto dto, Long strategyId, String mediaErrCode, boolean mediaRespFail, boolean isInvalid, int useTime) {
        if (mediaTag == null) {
            return;
        }
        RtbResponseDto responseDto = dto.getMediaDto().getResponse();
        boolean isParticipating = null != responseDto.getTags() && !responseDto.getTags().isEmpty();
        //媒体计数
        rtbMonitor.monitorMediaRequest(strategyId, mediaTag, 1, isInvalid ? 1 : 0, mediaRespFail ? 1 : 0, isParticipating ? 1 : 0, useTime);
        boolean success = LogicState.SUCCESS.getCode().equals(mediaErrCode);
        if (!CollectionUtils.isEmpty(dto.getAdvs())) {
            for (int i = 0; i < dto.getAdvs().size(); i++) {
                RtbAdvDto adv = dto.getAdvs().get(i);
                if (adv == null) {
                    continue;
                }
                //预算正常响应并且有填充
                boolean advFill = adv.getRtbResponseDto() != null && !CollectionUtils.isEmpty(adv.getRtbResponseDto().getTags());
                //媒体预算计数
                rtbMonitor.monitorMediaAdvRequest(strategyId, adv, mediaTag, 1, isInvalid ? 1 : 0, mediaRespFail ? 1 : 0, advFill ? 1 : 0, useTime);
            }
        }
        if (!success) {
            //媒体错误码计数
            rtbMonitor.monitorMediaErrorCode(strategyId, mediaTag, mediaErrCode);
        }
    }


    /**
     * 校验 媒体的请求参数
     */
    private SuperResult<Object> checkParam(Media media, RtbRequestDto request) {
        if (null == request) {
            return SuperResult.badResult(LogicState.ERROR_REQUEST_PARAM.getCode(), "param parse fail:" + media.getCode(), null);
        }
        // APP 校验
        RequestAppDto appInfo = request.getApp();
        if (appInfo == null) {
            return SuperResult.badResult(LogicState.ERROR_MEDIA_APP_PARAM.getCode(), "appInfo empty", null);
        }
        if (StringUtils.isEmpty(appInfo.getAppId())) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_APP.getCode(), "appId empty", null);
        }
        MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_CODE_ + media.getId() + "_" + appInfo.getAppId(), MediaApp.class);
        if (mediaApp == null) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_APP.getCode(), "app not found:" + appInfo.getAppId(), null);
        }
        if (mediaApp.getMediaId().longValue() != media.getId().longValue()) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_APP.getCode(), "app fail:" + appInfo.getAppId(), null);
        }
        if (StringUtils.isEmpty(appInfo.getAppVersion())) {
            return SuperResult.badResult(LogicState.ERROR_MEDIA_APP_PARAM.getCode(), "app version empty", null);
        }
        if (StringUtils.isEmpty(appInfo.getBundle())) {
            return SuperResult.badResult(LogicState.ERROR_MEDIA_APP_PARAM.getCode(), "app bundle empty", null);
        }

        // 广告位校验
        RequestTagDto tagInfo = request.getTag();
        if (tagInfo == null) {
            return SuperResult.badResult(LogicState.ERROR_MEDIA_TAG_PARAM.getCode(), "tagInfo empty", null);
        }
        if (StringUtils.isEmpty(tagInfo.getTagId())) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_TAG.getCode(), "tagId empty", null);
        }
        MediaTag mediaTag = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_TAG_CODE_ + media.getId() + "_" + mediaApp.getId() + "_" + tagInfo.getTagId(), MediaTag.class);
        if (null == mediaTag) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_TAG.getCode(), "tagId empty", null);
        }
        if (mediaTag.getMediaAppId().longValue() != mediaApp.getId().longValue()) {
            return SuperResult.badResult(LogicState.ERROR_NOT_FOUND_MEDIA_TAG.getCode(), "tagId fail:" + tagInfo.getTagId(), null);
        }
        // 媒体是否需要使用https
        tagInfo.setNeedHttps(mediaTag.getNeedHttps() == 1 || mediaTag.getMaterialHttps());
        // 媒体未传
        if (tagInfo.getWidth() == null && mediaTag.getWidth() != null) {
            tagInfo.setWidth(mediaTag.getWidth());
        }
        if (tagInfo.getHeight() == null && mediaTag.getHeight() != null) {
            tagInfo.setHeight(mediaTag.getHeight());
        }
        tagInfo.setTagType(TagType.findByType(mediaTag.getType()));
        if (BidType.BID.getType() == mediaTag.getBidType() && null == tagInfo.getPrice()) {
            return SuperResult.badResult(LogicState.ERROR_MEDIA_TAG_PARAM.getCode(), "price empty", null);
        }

        // 设备校验
        RequestDeviceDto deviceInfo = request.getDevice();
        if (null == deviceInfo) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE.getCode(), "device empty", null);
        }
        if (null == deviceInfo.getDeviceType()) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE.getCode(), "deviceType empty", null);
        }
        if (null == deviceInfo.getOsType()) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE.getCode(), "osType empty", null);
        }
        if (StringUtils.isEmpty(deviceInfo.getOsVersion())) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE_OS_VER.getCode(), "osVersion empty", null);
        }
        if (StringUtils.isEmpty(deviceInfo.getBrand())) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE_BRAND.getCode(), "brand empty", null);
        }
        if (StringUtils.isEmpty(deviceInfo.getModel())) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE_MODEL.getCode(), "model empty", null);
        }
        if (deviceInfo.getOsType() == OsType.IOS){
            if(!MODEL.matcher(deviceInfo.getModel()).matches()){
                return SuperResult.badResult(LogicState.ERROR_DEVICE_MODEL.getCode(), "model fail", null);
            }
        }
        if (StringUtils.isEmpty(deviceInfo.getUserAgent())) {
            return SuperResult.badResult(LogicState.ERROR_DEVICE_UA.getCode(), "user agent empty", null);
        }

        // 网络信息校验
        RequestNetworkDto networkInfo = request.getNetwork();
        if (null == networkInfo) {
            return SuperResult.badResult(LogicState.ERROR_NETWORK.getCode(), "network empty", null);
        }
        if (StringUtils.isEmpty(networkInfo.getIp())) {
            return SuperResult.badResult(LogicState.ERROR_NETWORK_IP.getCode(), "ip empty", null);
        }
        return SuperResult.rightResult();
    }

    /**
     * 预算广告位规则过滤
     */
    private SuperResult<String> checkFilter(MediaTag mediaTag, RtbRequestDto request, StrategyTagAdvertiser adv) {
        AdvertiserTag advertiserTag = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_TAG_ID_ + adv.getAdvertiserTagId(), AdvertiserTag.class);
        RequestDeviceDto device = request.getDevice();
        RequestNetworkDto network = request.getNetwork();
        RequestAppDto appDto = request.getApp();
        RequestGeoDto geoDto = request.getGeo();
        // 媒体广告位结算方式与预算广告位结算方式不同时 过滤掉
        /*if (mediaTag.getBidType() != null && mediaTag.getBidType() == 1 && !Objects.equals(mediaTag.getBidType(), advertiserTag.getSettlementType())) {
            return LogicState.ERROR_BID_TYPE.result();
        }*/
        // 竞价模式
        Double mediaPrice = request.getTag().getPrice();
        if (mediaTag.getBidType() == BidType.BID.getType()) {
            if (mediaPrice == null) {
                // 未传底价 过滤
                return LogicState.ERROR_PRICE.result();
            }
        }
        if (advertiserTag.getSettlementType() == BidType.BID.getType()) {
            if (mediaTag.getBidType() == BidType.BID.getType()) {
                if (advertiserTag.getBidPriceType() == SharingPriceType.FIXED.getType() && mediaPrice > advertiserTag.getFixedPrice()) {
                    // 预算使用固价，媒体请求的底价高于预算设置的固价 过滤
                    return LogicState.ERROR_PRICE.result();
                }
            }
        }
        if (mediaTag.getBidType() == BidType.BID.getType() && advertiserTag.getSettlementType() == BidType.SHARING.getType()) {
            // 竞价跑分成 出价信息未配置 过滤
            if (null == adv.getRtbToSharingBasePriceType() || null == adv.getRtbToSharingBidPriceType()) {
                return LogicState.ERROR_STRATEGY_NO_PRICE_INFO.result();
            }
            if (adv.getRtbToSharingBidPriceType() == BidPriceType.UP.getType()) {
                Integer rate = adv.getRtbToSharingBidRisesRatio();
                if (null == rate) {
                    return LogicState.ERROR_STRATEGY_NO_PRICE_INFO.result();
                }
                if (mediaPrice == 0d) {
                    return LogicState.ERROR_PRICE.result();
                }
                if (null != adv.getRtbToSharingMaxPrice()) {
                    double respMediaPrice = BigDecimal.valueOf(mediaPrice * (1 + (rate / 100.0d))).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    if (respMediaPrice > adv.getRtbToSharingMaxPrice()) {
                        // 涨幅后大于最高出价 过滤
                        return LogicState.ERROR_PRICE.result();
                    }
                }
            }
            if (adv.getRtbToSharingBidPriceType() == BidPriceType.FIXED.getType() && null == adv.getRtbToSharingFixedPrice()) {
                return LogicState.ERROR_STRATEGY_NO_PRICE_INFO.result();
            }
        }
        //基础规则
        SuperResult<String> result = checkBaseRule(advertiserTag, device);
        if (!result.getSuccess()) {
            return result;
        }
        //定向
        result = checkDirectRule(advertiserTag, device, network, appDto);
        if (!result.getSuccess()) {
            return result;
        }
        //过滤
        result = checkFilterRule(advertiserTag, device, network, appDto, geoDto);
        if (!result.getSuccess()) {
            return result;
        }
        // 策略中的规则
        result = checkStrategyRule(advertiserTag, appDto, adv);
        if (!result.getSuccess()) {
            return result;
        }
        return LogicState.SUCCESS.result();
    }

    /**
     * 校验 基础规则
     */
    public SuperResult<String> checkBaseRule(AdvertiserTag advertiserTag, RequestDeviceDto device) {
        if (advertiserTag.getLimitRuleOpen()) {
            String dayKey = DateUtils.toString(new Date(), "yyyyMMdd");
            String countField = advertiserTag.getId().toString();
            Set<String> deviceIds = device.toDeviceMd5List();

            // 限量
            if (!rtbCounter.tagIncrease(advertiserTag)) {
                return LogicState.ERROR_QUANTITY_LIMIT.result();
            }

            //时区
            if (null != advertiserTag.getStartTime() && null != advertiserTag.getEndTime()) {
                if (!isTimeInInterval(LocalTime.now(), LocalTime.of(advertiserTag.getStartTime(), 0), LocalTime.of(advertiserTag.getEndTime(), 59))) {
                    // 非时间区间内，过滤
                    return LogicState.ERROR_TIME_AREA_LIMIT.result();
                }
            }

            //曝光量
            if (advertiserTag.getFilterExposureNum() != null && advertiserTag.getFilterExposureNum() > 0) {
                Long n = baseRedisL2Cache.get(BaseRedisKeys.KV_COUNT_ADVERTISER_TAG_EXPOSURE_ + dayKey + "_" + countField, Long.class);
                if (n != null && n.compareTo(advertiserTag.getFilterExposureNum()) >= 0) {
                    return LogicState.ERROR_EXPOSURE_LIMIT.result();
                }
            }

            //点击量
            if (advertiserTag.getFilterClickNum() != null && advertiserTag.getFilterClickNum() > 0) {
                Long n = baseRedisL2Cache.get(BaseRedisKeys.KV_COUNT_ADVERTISER_TAG_CLICK_ + dayKey + "_" + countField, Long.class);
                if (n != null && n.compareTo(advertiserTag.getFilterClickNum()) >= 0) {
                    return LogicState.ERROR_CLICK_LIMIT.result();
                }
            }

            //单设备曝光
            if (advertiserTag.getFilterDeviceExposureNum() != null && advertiserTag.getFilterDeviceExposureNum() > 0 && deviceIds != null) {
                for (String id : deviceIds) {
                    Long n = redis.get(BaseRedisKeys.KV_D_C_E_ + dayKey + "_" + countField + "_" + id, Long.class);
                    if (n != null && n.intValue() >= advertiserTag.getFilterDeviceExposureNum()) {
                        return LogicState.ERROR_DEVICE_EXPOSURE_LIMIT.result();
                    }
                }
            }

            //单设备请求
            if (advertiserTag.getFilterDeviceReqNum() != null && advertiserTag.getFilterDeviceReqNum() > 0 && deviceIds != null) {
                for (String id : deviceIds) {
                    int expired = ExpireUtils.getRandom6TodayExpireSeconds();
                    long n = redis.incr(BaseRedisKeys.KV_D_C_R_ + dayKey + "_" + countField + "_" + id);
                    redis.expire(BaseRedisKeys.KV_D_C_R_ + dayKey + "_" + countField + "_" + id, expired);
                    if (n >= advertiserTag.getFilterDeviceReqNum()) {
                        return LogicState.ERROR_DEVICE_RQE_LIMIT.result();
                    }
                }
            }

        }
        return LogicState.SUCCESS.result();
    }

    /**
     * 校验 定向规则
     */
    public SuperResult<String> checkDirectRule(AdvertiserTag advertiserTag, RequestDeviceDto device, RequestNetworkDto network, RequestAppDto appDto) {
        if (advertiserTag.getDirectOnOff()) {
            //省/市
            if (StringUtils.isNotBlank(advertiserTag.getTargetArea()) && StringUtils.isNotEmpty(network.getIp())) {
                JsonArray array = JsonHelper.fromJson(JsonElement.class, advertiserTag.getTargetArea()).getAsJsonArray();
                if (!array.isEmpty()) {
                    IpInfo ipInfo = ip2regionSearcher.memorySearch(network.getIp());
                    if (null == ipInfo || StringUtils.isEmpty(ipInfo.getProvince())) {
                        // 未知地域 过滤
                        return LogicState.ERROR_DIRECT_CITY.result();
                    }
                    for (int i = 0; i < array.size(); i++) {
                        JsonArray next = array.get(i).getAsJsonArray();
                        String provinceCode = next.get(0).getAsString();
                        String cityCode = next.get(1).getAsString();
                        BaseProvince province = baseRedisL2Cache.get(BaseRedisKeys.KV_BASE_PROVINCE_CODE_ + provinceCode, BaseProvince.class);
                        if (null != province && !province.getName().contains(ipInfo.getProvince())) {
                            return LogicState.ERROR_DIRECT_CITY.result();
                        }
                        BaseCity city = baseRedisL2Cache.get(BaseRedisKeys.KV_BASE_CITY_CODE_ + cityCode, BaseCity.class);
                        if (null != city) {
                            if (StringUtils.isEmpty(ipInfo.getCity()) || !city.getName().contains(ipInfo.getCity())) {
                                // 获取不到城市或城市不匹配 过滤
                                return LogicState.ERROR_DIRECT_CITY.result();
                            }
                        }
                    }
                }
            }

            //行业
            if (StringUtils.isNotEmpty(advertiserTag.getTargetAppIndustry())) {
                AdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + advertiserTag.getAdvertiserAppId(), AdvertiserApp.class);
                JsonArray array = JsonHelper.fromJson(JsonElement.class, advertiserTag.getTargetAppIndustry()).getAsJsonArray();
                if (!array.isEmpty()) {
                    Set<Long> firstIds = new HashSet<>();
                    Set<Long> secondIds = new HashSet<>();
                    for (int i = 0; i < array.size(); i++) {
                        JsonArray next = array.get(i).getAsJsonArray();
                        firstIds.add(next.get(0).getAsLong());
                        secondIds.add(next.get(1).getAsLong());
                    }
                    boolean r = firstIds.contains(app.getFirstIndustryId()) && secondIds.contains(app.getSecondIndustryId());
                    if (!r) {
                        return LogicState.ERROR_DIRECT_INDUSTRY.result();
                    }
                }
            }

            //包名称
            if (StringUtils.isNotEmpty(advertiserTag.getTargetAppPackage())) {
                String packName = appDto.getBundle();
                if (!advertiserTag.getTargetAppPackage().contains(packName)) {
                    return LogicState.ERROR_DIRECT_APP_PACK_NAME.result();
                }
            }
            //设备机型
            if (StringUtils.isNotBlank(advertiserTag.getTargetDeviceBrand())) {
                Integer[] targetBrands = JsonHelper.fromJson(Integer[].class, advertiserTag.getTargetDeviceBrand());
                List<String> brands = DeviceBrand.parseCodesById(targetBrands);
                DeviceBrand requestBrand = DeviceBrand.parseByBrand(device.getBrand());
                if (!brands.isEmpty() && !brands.contains(requestBrand.getBrand())) {
                    return LogicState.ERROR_DIRECT_DEVICE_BRAND.result();
                }
            }
            //设备型号
            if (StringUtils.isNotBlank(advertiserTag.getTargetDeviceModel())) {
                if (!advertiserTag.getTargetDeviceModel().contains(device.getModel())) {
                    return LogicState.ERROR_DIRECT_DEVICE_MODEL.result();
                }
            }
            //过滤网络类型
            if (StringUtils.isNotBlank(advertiserTag.getTargetNetwork())) {
                Integer[] codes = JsonHelper.fromJson(Integer[].class, advertiserTag.getTargetNetwork());
                if (codes != null && codes.length > 0 && !Arrays.asList(codes).contains(network.getConnectType().getType())) {
                    return LogicState.ERROR_DIRECT_NET_WORK_MODEL.result();
                }
            }
            //过滤运营商
            if (StringUtils.isNotBlank(advertiserTag.getTargetOperator())) {
                Integer[] codes = JsonHelper.fromJson(Integer[].class, advertiserTag.getTargetOperator());
                if (codes != null && codes.length > 0 && !Arrays.asList(codes).contains(network.getCarrierType().getType())) {
                    return LogicState.ERROR_DIRECT_OPERATOR.result();
                }
            }
            // 版本号
            if (StringUtils.isNotEmpty(advertiserTag.getTargetOsVersion())) {
                if (advertiserTag.getTargetOsVersionType() > CompareType.UNLIMITED.getType()) { // 大于 1不限制
                    int result = CompareType.compareVersions(device.getOsVersion(), advertiserTag.getTargetOsVersion());
                    if (advertiserTag.getTargetOsVersionType() == CompareType.GREATER_THAN.getType()) { // 大于
                        if (result != 1) { // reqVersion 不能小于等于 target
                            return LogicState.ERROR_DIRECT_DEVICE_VERSION.result();
                        }
                    } else if (advertiserTag.getTargetOsVersionType() == CompareType.GREATER_THAN_OR_EQUAL_TO.getType()) { //大于等于
                        if (result == -1) { // reqVersion 小于 target
                            return LogicState.ERROR_DIRECT_DEVICE_VERSION.result();
                        }
                    } else if (advertiserTag.getTargetOsVersionType() == CompareType.LESS_THAN.getType()) { // 小于
                        if (result != -1) { // reqVersion 大于 等于 target
                            return LogicState.ERROR_DIRECT_DEVICE_VERSION.result();
                        }
                    } else if (advertiserTag.getTargetOsVersionType() == CompareType.LESS_THAN_OR_EQUAL_TO.getType()) { //小于等于
                        if (result == 1) { // reqVersion 大于 target
                            return LogicState.ERROR_DIRECT_DEVICE_VERSION.result();
                        }
                    } else if (advertiserTag.getTargetOsVersionType() == CompareType.EQUAL_TO.getType()) { //等于
                        if (result != 0) { // 不等于
                            return LogicState.ERROR_DIRECT_DEVICE_VERSION.result();
                        }
                    }
                }
            }

            // 定向已安装APP包名
            if (StringUtils.isNotBlank(advertiserTag.getTargetInstalledAppPackage())) {
                if (device.getInstalledAppInfo() == null || device.getInstalledAppInfo().isEmpty()) {
                    return LogicState.ERROR_DIRECT_INSTALLED_APP.result();
                }
                Set<String> installedApps = device.getInstalledAppInfo().stream().map(RequestInstalledAppDto::getPackageName).collect(Collectors.toSet());
                List<String> packageNames = Arrays.asList(advertiserTag.getTargetInstalledAppPackage().split(","));
                if (!installedApps.containsAll(packageNames)) {
                    return LogicState.ERROR_DIRECT_INSTALLED_APP.result();
                }
            }
            // 人群包
            if (advertiserTag.getTargetPackId() != null) {
                DmpPackage pack = baseRedisL2Cache.get(BaseRedisKeys.KV_DMP_PACK_ID_ + advertiserTag.getTargetPackId(), DmpPackage.class);
                if (pack != null && pack.getState() == 1) {
                    if (!DmpDeviceCacheUtils.isPackDevice(redis, pack.getId(), device)) {
                        return LogicState.ERROR_DIRECT_DMP_PACKAGE.result();
                    }
                }
            }
        }
        return LogicState.SUCCESS.result();
    }

    /**
     * 校验 过滤规则
     */
    public SuperResult<String> checkFilterRule(AdvertiserTag advertiserTag, RequestDeviceDto device, RequestNetworkDto network, RequestAppDto appDto, RequestGeoDto geoDto) {
        if (advertiserTag.getFilterOnOff()) {
            //是否过滤国外IP
            if (advertiserTag.getFilterForeignIp() != null && advertiserTag.getFilterForeignIp() == 1) {
                boolean isFilter = false;
                if (StringUtils.isNotEmpty(network.getIp())) {
                    // 根据IP过滤
                    IpInfo ipInfo = ip2regionSearcher.memorySearch(network.getIp());
                    if (null != ipInfo && StringUtils.isNotEmpty(ipInfo.getCountry())) {
                        isFilter = true;
                        if (!ipInfo.getCountry().equals("中国")) {
                            return LogicState.ERROR_CN_IP.result();
                        }
                    }
                }
                if (!isFilter && null != geoDto.getLatitude() && null != geoDto.getLongitude()) {
                    isFilter = true;
                    // 根据地区过滤
                    if (!ChinaGeoFenceUtil.isPointInChina(geoDto.getLongitude(), geoDto.getLatitude())) {
                        return LogicState.ERROR_CN_IP.result();
                    }
                }
            }

            //省/市
            if (StringUtils.isNotBlank(advertiserTag.getFilterArea())) {
                IpInfo ipInfo = ip2regionSearcher.memorySearch(network.getIp());
                if (ipInfo != null) {
                    JsonArray array = JsonHelper.fromJson(JsonElement.class, advertiserTag.getFilterArea()).getAsJsonArray();
                    if (!array.isEmpty()) {
                        for (int i = 0; i < array.size(); i++) {
                            JsonArray next = array.get(i).getAsJsonArray();
                            String provinceCode = next.get(0).getAsString();
                            String cityCode = next.get(1).getAsString();

                            BaseProvince province = baseRedisL2Cache.get(BaseRedisKeys.KV_BASE_PROVINCE_CODE_ + provinceCode, BaseProvince.class);
                            if (null != province && StringUtils.isNotEmpty(ipInfo.getProvince()) && province.getName().contains(ipInfo.getProvince())) {
                                return LogicState.ERROR_DIRECT_CITY.result();
                            }
                            BaseCity city = baseRedisL2Cache.get(BaseRedisKeys.KV_BASE_CITY_CODE_ + cityCode, BaseCity.class);
                            if (null != city && StringUtils.isNotEmpty(ipInfo.getCity()) && city.getName().contains(ipInfo.getCity())) {
                                return LogicState.ERROR_DIRECT_CITY.result();
                            }
                        }
                    }
                }
            }

            //行业
            if (StringUtils.isNotBlank(advertiserTag.getFilterAppIndustry())) {
                AdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + advertiserTag.getAdvertiserAppId(), AdvertiserApp.class);
                JsonArray array = JsonHelper.fromJson(JsonElement.class, advertiserTag.getFilterAppIndustry()).getAsJsonArray();
                if (!array.isEmpty()) {
                    Set<Long> firstIds = new HashSet<>();
                    Set<Long> secondIds = new HashSet<>();
                    for (int i = 0; i < array.size(); i++) {
                        JsonArray next = array.get(i).getAsJsonArray();
                        firstIds.add(next.get(0).getAsLong());
                        secondIds.add(next.get(1).getAsLong());
                    }
                    boolean r = firstIds.contains(app.getFirstIndustryId()) && secondIds.contains(app.getSecondIndustryId());
                    if (r) {
                        return LogicState.ERROR_FILTER_INDUSTRY.result();
                    }
                }
            }

            //包名称
            if (StringUtils.isNotBlank(advertiserTag.getFilterAppPackage())) {
                String name = appDto.getBundle();
                if (advertiserTag.getFilterAppPackage().contains(name)) {
                    return LogicState.ERROR_FILTER_APP_PACK_NAME.result();
                }
            }
            //设备机型
            if (StringUtils.isNotBlank(advertiserTag.getFilterDeviceBrand())) {
                Integer[] targetBrands = JsonHelper.fromJson(Integer[].class, advertiserTag.getFilterDeviceBrand());
                List<String> brands = DeviceBrand.parseCodesById(targetBrands);
                DeviceBrand requestBrand = DeviceBrand.parseByBrand(device.getBrand());
                if (!brands.isEmpty() && brands.contains(requestBrand.getBrand())) {
                    return LogicState.ERROR_FILTER_DEVICE_BRAND.result();
                }
            }
            //设备型号
            if (StringUtils.isNotBlank(advertiserTag.getFilterDeviceModel())) {
                if (advertiserTag.getFilterDeviceModel().contains(device.getModel())) {
                    return LogicState.ERROR_FILTER_DEVICE_MODEL.result();
                }
            }
            //过滤空设备
            if (advertiserTag.getFilterEmptyDevice() != null && advertiserTag.getFilterEmptyDevice() == 1) {
                // 暂时只过滤 安卓或IOS
                if (device.getOsType() == OsType.ANDROID) {
                    if (StringUtils.isBlank(device.getImei()) && StringUtils.isBlank(device.getImeiMd5())
                            && StringUtils.isBlank(device.getOaid()) && StringUtils.isBlank(device.getOaidMd5())
                            && StringUtils.isBlank(device.getAndroidId()) && StringUtils.isBlank(device.getAndroidIdMd5())
                            && StringUtils.isBlank(device.getVaid()) && StringUtils.isBlank(device.getVaidMd5())) {
                        return LogicState.ERROR_FILTER_EMPTY_DEVICE.result();
                    }
                } else if (device.getOsType() == OsType.IOS) {
                    if (StringUtils.isBlank(device.getIdfa()) && StringUtils.isBlank(device.getIdfaMd5())
                            && StringUtils.isBlank(device.getIdfv()) && StringUtils.isBlank(device.getIdfvMd5())) {
                        if (CollectionUtils.isEmpty(device.getCaids())) {
                            return LogicState.ERROR_FILTER_EMPTY_DEVICE.result();
                        }
                        if (device.getCaids().stream().anyMatch(dto -> StringUtils.isBlank(dto.getCaid()))) {
                            return LogicState.ERROR_FILTER_EMPTY_DEVICE.result();
                        }
                    }
                }
            }
            //过滤无效设备
            if (advertiserTag.getFilterInvalidDevice() != null && advertiserTag.getFilterInvalidDevice() == 1) {
                // 启动标识、更新标识未传
                if (StringUtils.isEmpty(device.getUpdateMark()) || StringUtils.isEmpty(device.getBootMark())) {
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result("boot/update mark empty");
                }
                // 三大时间参数空校验
                if (StringUtils.isEmpty(device.getSysInitTime()) || StringUtils.isEmpty(device.getSysStartTime()) || StringUtils.isEmpty(device.getSysUpdateTime())) {
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result("time empty");
                }
                // paid 参数校验
                if (StringUtils.isEmpty(device.getPaid())) {
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result("paid empty");
                }
                OsType osType = device.getOsType();
                String osVersion = device.getOsVersion();
                String[] osVer = osVersion.split("\\.");
                if (!NumberUtils.isDigits(osVer[0])) {
                    // 非数字直接过滤
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result("osVersion not num");
                }

                // UserAgent 校验
                String ua = device.getUserAgent();
                if (!ua.startsWith("Mozilla")) {
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result("ua error");
                }
                if (ua.length() < 20 || ua.length() > 512) {
                    return LogicState.ERROR_FILTER_INVALID_DEVICE.result();
                }
                int osNum = Integer.parseInt(osVer[0]);
                if (osType == OsType.ANDROID) {
                    // Androidid缺失
                    if (StringUtils.isEmpty(device.getAndroidId()) && StringUtils.isEmpty(device.getAndroidIdMd5())) {
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("androidId empty");
                    }
                    // imei或者oaid缺失/错误（10版本以下必传imei；10及以上版本必传oaid）
                    if (osNum < 10 && StringUtils.isEmpty(device.getImei()) && StringUtils.isEmpty(device.getImeiMd5())) {
                        // 10版本以下必传imei
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("imei empty");
                    }
                    if (osNum >= 10 && StringUtils.isEmpty(device.getOaid()) && StringUtils.isEmpty(device.getOaidMd5())) {
                        // 10及以上版本必传oaid
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("oaid empty");
                    }
                    if (!ua.contains(osVersion)) {
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("ua error");
                    }
                    if (!ua.contains("Android")) {
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("ua error");
                    }
                }
                if (osType == OsType.IOS) {
                    if (osNum >= 14 && CollectionUtils.isEmpty(device.getCaids()) && StringUtils.isEmpty(device.getIdfa()) && StringUtils.isEmpty(device.getIdfaMd5())) {
                        // 14版本以上 caid idfa 至少传1个
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("caids empty");
                    }
                    if (osNum < 14 && StringUtils.isEmpty(device.getIdfa()) && StringUtils.isEmpty(device.getIdfaMd5())) {
                        // 14以下 必传idfa
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("idfa empty");
                    }
                    osVersion = osVersion.replace(".", "_");
                    if (!ua.contains(osVersion)) {
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("ua error");
                    }
                    if (!ua.contains("iPhone")) {
                        return LogicState.ERROR_FILTER_INVALID_DEVICE.result("ua error");
                    }
                }
            }
            //过滤网络类型
            if (StringUtils.isNotBlank(advertiserTag.getFilterNetwork())) {
                Integer[] codes = JsonHelper.fromJson(Integer[].class, advertiserTag.getFilterNetwork());
                if (Arrays.asList(codes).contains(network.getConnectType().getType())) {
                    return LogicState.ERROR_FILTER_NET_WORK_MODEL.result();
                }
            }
            //过滤运营商
            if (StringUtils.isNotBlank(advertiserTag.getFilterOperator())) {
                Integer[] codes = JsonHelper.fromJson(Integer[].class, advertiserTag.getFilterOperator());
                if (Arrays.asList(codes).contains(network.getCarrierType().getType())) {
                    return LogicState.ERROR_FILTER_OPERATOR.result();
                }
            }
            // 过滤域名 --预算返回填充校验
            // 过滤已安装APP包名
            if (StringUtils.isNotBlank(advertiserTag.getFilterInstalledAppPackage())) {
                if (device.getInstalledAppInfo() != null && !device.getInstalledAppInfo().isEmpty()) {
                    List<String> packageNames = Arrays.asList(advertiserTag.getFilterInstalledAppPackage().split(","));
                    for (RequestInstalledAppDto requestInstalledAppDto : device.getInstalledAppInfo()) {
                        if (packageNames.contains(requestInstalledAppDto.getPackageName())) {
                            return LogicState.ERROR_FILTER_INSTALLED_APP.result();
                        }
                    }
                }
            }
            // 人群包
            if (advertiserTag.getFilterPackId() != null) {
                DmpPackage pack = baseRedisL2Cache.get(BaseRedisKeys.KV_DMP_PACK_ID_ + advertiserTag.getFilterPackId(), DmpPackage.class);
                if (pack != null && pack.getState() == 1) {
                    if (DmpDeviceCacheUtils.isPackDevice(redis, pack.getId(), device)) {
                        return LogicState.ERROR_FILTER_DMP_PACKAGE.result();
                    }
                }
            }
        }
        return LogicState.SUCCESS.result();
    }

    /**
     * 校验策略中的规则
     */
    public SuperResult<String> checkStrategyRule(AdvertiserTag advertiserTag, RequestAppDto appDto, StrategyTagAdvertiser strategyTagAdv) {
        //随机策略-qps
        boolean r = rtbCounter.strategyTagQpsIncrease(strategyTagAdv, advertiserTag);
        if (!r) {
            return LogicState.ERROR_STRATEGY_ADV_TAG_LIMIT.result();
        }

        // app 参数校验
        if (strategyTagAdv.getHandleType() != null && strategyTagAdv.getHandleType() > 0) {
            AdvertiserApp app = baseRedisL2Cache.get(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + strategyTagAdv.getAdvertiserAppId(), AdvertiserApp.class);
            if (strategyTagAdv.getHandleType() == 1) {
                if (StringUtils.isNotEmpty(app.getPackageName()) && !app.getPackageName().equals(appDto.getBundle())) {
                    return LogicState.ERROR_CHECK_APP_NAME.result();
                }
            }
        }
        return LogicState.SUCCESS.result();
    }

    /**
     * 判断指定时间是否在给定的小时区间内，支持跨天的情况
     *
     * @param time      要检查的时间
     * @param startTime 区间的开始时间
     * @param endTime   区间的结束时间
     * @return 如果时间在区间内返回 true，否则返回 false
     */
    public static boolean isTimeInInterval(LocalTime time, LocalTime startTime, LocalTime endTime) {
        if (startTime.isBefore(endTime)) {
            // 非跨天情况，直接判断时间是否在区间内
            return !time.isBefore(startTime) && !time.isAfter(endTime);
        } else {
            // 跨天情况，判断时间是否在区间内
            return time.isAfter(startTime) || time.isBefore(endTime);
        }
    }

}

