package cn.taken.ad.configuration.web.captcha;

import java.awt.image.BufferedImage;

public class ImageCaptcha {

    /**
     * 验证码
     */
    private String text;
    /**
     * 验证码图片
     */
    private BufferedImage bufferedImage;

    public ImageCaptcha() {

    }

    public ImageCaptcha(String text, BufferedImage bufferedImage) {
        this.text = text;
        this.bufferedImage = bufferedImage;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public BufferedImage getBufferedImage() {
        return bufferedImage;
    }

    public void setBufferedImage(BufferedImage bufferedImage) {
        this.bufferedImage = bufferedImage;
    }

}