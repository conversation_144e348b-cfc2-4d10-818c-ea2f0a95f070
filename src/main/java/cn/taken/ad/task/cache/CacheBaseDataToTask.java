package cn.taken.ad.task.cache;

import cn.taken.ad.component.redis.RedisClient;
import cn.taken.ad.component.superscheduler.core.SuperScheduled;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaProtocol;
import cn.taken.ad.core.pojo.strategy.StrategyTagAdvertiser;
import cn.taken.ad.core.service.advertiser.AdvertiserAppService;
import cn.taken.ad.core.service.advertiser.AdvertiserProtocolService;
import cn.taken.ad.core.service.advertiser.AdvertiserService;
import cn.taken.ad.core.service.advertiser.AdvertiserTagService;
import cn.taken.ad.core.service.base.BaseAppIndustryService;
import cn.taken.ad.core.service.base.BaseCityService;
import cn.taken.ad.core.service.base.BaseProvinceService;
import cn.taken.ad.core.service.dmp.DmpPackageService;
import cn.taken.ad.core.service.dsp.account.DspAdvertiserAccountService;
import cn.taken.ad.core.service.dsp.ad.DspAdvertiserAdService;
import cn.taken.ad.core.service.dsp.ad.DspAdvertiserResourceService;
import cn.taken.ad.core.service.dsp.app.DspAdvertiserAppService;
import cn.taken.ad.core.service.media.MediaAppService;
import cn.taken.ad.core.service.media.MediaProtocolService;
import cn.taken.ad.core.service.media.MediaService;
import cn.taken.ad.core.service.media.MediaTagService;
import cn.taken.ad.core.service.strategy.StrategyService;
import cn.taken.ad.core.service.strategy.StrategyTagAdvertiserService;
import cn.taken.ad.utils.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
public class CacheBaseDataToTask {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Resource(name = "BaseRedis")
    private RedisClient redis;
    @Resource
    private MediaService mediaService;
    @Resource
    private MediaAppService mediaAppService;
    @Resource
    private MediaTagService mediaTagService;
    @Resource
    private MediaProtocolService mediaProtocolService;
    @Resource
    private AdvertiserService advertiserService;
    @Resource
    private AdvertiserAppService advertiserAppService;
    @Resource
    private AdvertiserTagService advertiserTagService;
    @Resource
    private AdvertiserProtocolService advertiserProtocolService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private StrategyTagAdvertiserService strategyTagAdvertiserService;
    @Resource
    private BaseProvinceService baseProvinceService;
    @Resource
    private BaseCityService baseCityService;
    @Resource
    private BaseAppIndustryService baseAppIndustryService;
    @Resource
    private DspAdvertiserAppService dspAdvertiserAppService;
    @Resource
    private DspAdvertiserResourceService dspAdvertiserResourceService;
    @Resource
    private DspAdvertiserAdService dspAdvertiserAdService;
    @Resource
    private DspAdvertiserAccountService dspAdvertiserAccountService;
    @Resource
    private DmpPackageService dmpPackageService;


    private Date lastUpdateTime;

    @SuperScheduled(initialDelay = 1000L, fixedDelay = 30L * 1000L)
    public void cache() {
        logger.info("cache base data to redis begin");
        long now = System.currentTimeMillis();
        Long lastTime = redis.get(BaseRedisKeys.KV_CACHE_BASE_DATA_LAST_TIME, Long.class);
        lastUpdateTime = new Date(lastTime == null || lastTime < 0L ? 0L : lastTime);
        this.cacheIndustry();
        this.cacheProvince();
        this.cacheCity();

        this.cacheMediaProtocol();
        this.cacheMedia();
        this.cacheMediaApp();
        this.cacheMediaTag();


        this.cacheAdvertiser();
        this.cacheAdvertiserApp();
        this.cacheAdvertiserTag();
        this.cacheAdvertiserProtocol();

        this.cacheStrategy();
        this.cacheStrategyTagAdvertiser();
        //dsp
        this.cacheDspApp();
        this.cacheDspResource();
        this.cacheDspAd();
        this.cacheDspAccount();
        //dmp
        this.cacheDmpPackage();

        now = now - 10L * 1000L;// 前推十秒
        redis.set(BaseRedisKeys.KV_CACHE_BASE_DATA_LAST_TIME, now, -1);
        logger.info("cache base data to redis end");
    }

    private void cacheIndustry() {
        this.execute(
                "BaseIndustry",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> baseAppIndustryService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_BASE_INDUSTRY_ID_ + item.getId(), item, -1);
                }
        );
    }

    private void cacheProvince() {
        this.execute(
                "BaseProvince",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> baseProvinceService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_BASE_PROVINCE_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_BASE_PROVINCE_CODE_ + item.getCode(), item, -1);
                });
    }


    private void cacheCity() {
        this.execute(
                "BaseCity",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> baseCityService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_BASE_CITY_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_BASE_CITY_CODE_ + item.getCode(), item, -1);
                });
    }

    private void cacheMedia() {
        this.execute(
                "Media",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> mediaService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_MEDIA_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_MEDIA_CODE_ + item.getCode(), item, -1);
                });
    }

    private void cacheMediaTag() {
        this.execute(
                "MediaTag",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> mediaTagService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_MEDIA_TAG_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_MEDIA_TAG_CODE_ + item.getMediaId() + "_" + item.getMediaAppId() + "_" + item.getCode(), item, -1);
                    // 获取协议 判断是否需要AppCode
                    Media media = redis.get(BaseRedisKeys.KV_MEDIA_ID_ + item.getMediaId(), Media.class);
                    if (null != media) {
                        MediaProtocol mediaProtocol = redis.get(BaseRedisKeys.KV_MEDIA_PROTOCOL_ID_ + media.getProtocolId(), MediaProtocol.class);
                        if (null != mediaProtocol && !Boolean.TRUE.equals(mediaProtocol.getIsNeedAppCode())) {
                            redis.set(BaseRedisKeys.KV_MEDIA_NOAPPCODE_TAG_CODE_ + item.getMediaId() + "_" + item.getCode(), item, -1);
                        }
                    }

                });
    }

    private void cacheMediaApp() {
        this.execute(
                "MediaApp",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> mediaAppService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_MEDIA_APP_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_MEDIA_APP_CODE_ + item.getMediaId() + "_" + item.getCode(), item, -1);
                });
    }

    private void cacheMediaProtocol() {
        this.execute(
                "MediaProtocol",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> mediaProtocolService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_MEDIA_PROTOCOL_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheAdvertiser() {
        this.execute(
                "Advertiser",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> advertiserService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_ADVERTISER_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheAdvertiserApp() {
        this.execute(
                "AdvertiserApp",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> advertiserAppService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_ADVERTISER_APP_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheAdvertiserTag() {
        this.execute(
                "AdvertiserTag",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> advertiserTagService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_ADVERTISER_TAG_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheAdvertiserProtocol() {
        this.execute(
                "AdvertiserProtocol",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> advertiserProtocolService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_ADVERTISER_PROTOCOL_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheStrategy() {
        this.execute(
                "Strategy",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> strategyService.findByLastUpDateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_STRATEGY_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_STRATEGY_MEDIA_TAG_ID_ + item.getMediaTagId(), item, -1);
                    List<StrategyTagAdvertiser> list = strategyTagAdvertiserService.findStrategyTags(item.getId());
                    List<Long> ids = new LinkedList<>();
                    if (!CollectionUtils.isEmpty(list)) {
                        ids = list.stream().map(StrategyTagAdvertiser::getId).collect(Collectors.toList());
                    }
                    redis.set(BaseRedisKeys.KV_STRATEGY_MEDIA_TAG_ADVERTISER_TAGS_ID_ + item.getId(), ids, -1);
                });
    }


    private void cacheStrategyTagAdvertiser() {
        this.execute(
                "StrategyTagAdvertiser",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> strategyTagAdvertiserService.findByLastUpDateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_STRATEGY_ADVERTISER_TAG_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheDspApp() {
        this.execute(
                "DspAdvertiserApp",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> dspAdvertiserAppService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_DSP_ADVERTISER_APP_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheDspResource() {
        this.execute(
                "DspAdvertiserResource",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> dspAdvertiserResourceService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_DSP_ADVERTISER_RESOURCE_ID_ + item.getId(), item, -1);
                });
    }

    private void cacheDspAd() {
        Map<Long, List<String>> accountAddMap = new ConcurrentHashMap<>();
        Map<Long, List<String>> accountRemoveMap = new ConcurrentHashMap<>();
        this.execute(
                "DspAdvertiserAd",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> dspAdvertiserAdService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_DSP_ADVERTISER_AD_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_DSP_ADVERTISER_AD_CODE_ + item.getAdId(), item, -1);
                    if (item.getAccountId() != null) {
                        if (item.getStatus() != null && item.getStatus() == 1) {
                            accountAddMap.computeIfAbsent(item.getAccountId(), k -> new LinkedList<>()).add(item.getAdId());
                        } else {
                            accountRemoveMap.computeIfAbsent(item.getAccountId(), k -> new LinkedList<>()).add(item.getAdId());
                        }
                    }
                    if (accountRemoveMap.size() % 100 == 0) {
                        this.updateAccountAd(accountRemoveMap, true);
                    }
                    if (accountAddMap.size() % 100 == 0) {
                        this.updateAccountAd(accountAddMap, false);
                    }
                });
        this.updateAccountAd(accountRemoveMap, true);
        this.updateAccountAd(accountAddMap, false);
    }

    private void updateAccountAd(Map<Long, List<String>> map, boolean isDelete) {
        if (map.isEmpty()) {
            return;
        }
        map.forEach((k, v) -> {
            String[] olds = redis.get(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_CREATIVE_IDS_ + k, String[].class);
            if (olds != null) {
                List<String> all = ListUtils.asList(olds);
                if (isDelete) {
                    all.removeAll(v);
                    if (all.isEmpty()) {
                        redis.del(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_CREATIVE_IDS_ + k);
                    } else {
                        redis.set(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_CREATIVE_IDS_ + k, all, -1);
                    }
                } else {
                    v.addAll(all);
                }
            }
            if (!isDelete) {
                redis.set(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_CREATIVE_IDS_ + k, new HashSet<>(v), -1);
            }
        });
        map.clear();
    }

    private void cacheDspAccount() {
        this.execute(
                "DspAdvertiserAccount",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> dspAdvertiserAccountService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_ID_ + item.getId(), item, -1);
                    redis.set(BaseRedisKeys.KV_DSP_ADV_ACCOUNT_CODE_ + item.getAccountId(), item, -1);
                });
    }

    private void cacheDmpPackage() {
        this.execute(
                "DmpPackage",
                lastUpdateTime,
                (lastUpdateTime, start, limit) -> dmpPackageService.findByLastUpdateTime(lastUpdateTime, start, limit),
                (item) -> {
                    redis.set(BaseRedisKeys.KV_DMP_PACK_ID_ + item.getId(), item, -1);
                    if (item.getType() == 2) {
                        if (item.getState() == 1) {
                            redis.sadd(BaseRedisKeys.SET_DMP_PACK_REAL_TIME, -1, item.getId());
                        } else {
                            redis.srem(BaseRedisKeys.SET_DMP_PACK_REAL_TIME, item.getId().toString());
                        }
                    }
                });
    }

    private <T> void execute(String name, Date lastUpdateTime, DataQuery<T> query, DataHandler<T> handler) {
        int total = 0;
        int start = 0;
        int limit = 1000;
        while (true) {
            List<T> items = query.query(lastUpdateTime, start, limit);
            if (items == null || items.isEmpty()) {
                break;
            }
            items.forEach(handler::exec);
            start += limit;
            total += items.size();
        }
        logger.info("cache base data execute {} total:{}", name, total);
    }

    interface DataQuery<T> {
        List<T> query(Date lastUpdateTime, int start, int limit);
    }

    interface DataHandler<T> {
        void exec(T item);
    }

}
