package cn.taken.ad.core.dto.web.oper.statistics.merge;

import javax.validation.constraints.NotNull;

public class StatisticsMediaAdvertiserRequestExportReq {

    @NotNull(message = "维度未选择")
    private String statisticsType;
    private String beginTime;
    private String endTime;
    private Long mediaTagId;
    private Long mediaId;
    private Long mediaAppId;
    private Long advertiserId;
    private Long advertiserAppId;
    private Long advertiserTagId;
    private Integer bidType;
    private Integer settlementType;

    public String getStatisticsType() {
        return statisticsType;
    }

    public void setStatisticsType(String statisticsType) {
        this.statisticsType = statisticsType;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getMediaTagId() {
        return mediaTagId;
    }

    public void setMediaTagId(Long mediaTagId) {
        this.mediaTagId = mediaTagId;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }

    public Long getMediaAppId() {
        return mediaAppId;
    }

    public void setMediaAppId(Long mediaAppId) {
        this.mediaAppId = mediaAppId;
    }

    public Long getAdvertiserId() {
        return advertiserId;
    }

    public void setAdvertiserId(Long advertiserId) {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserAppId() {
        return advertiserAppId;
    }

    public void setAdvertiserAppId(Long advertiserAppId) {
        this.advertiserAppId = advertiserAppId;
    }

    public Long getAdvertiserTagId() {
        return advertiserTagId;
    }

    public void setAdvertiserTagId(Long advertiserTagId) {
        this.advertiserTagId = advertiserTagId;
    }

    public Integer getBidType() {
        return bidType;
    }

    public void setBidType(Integer bidType) {
        this.bidType = bidType;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }
}
