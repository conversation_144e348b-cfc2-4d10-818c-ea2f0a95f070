package cn.taken.ad.core.dao.strategy;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.core.dto.web.oper.strategy.main.StrategyInfo;
import cn.taken.ad.core.dto.web.oper.strategy.main.StrategyPageReq;
import cn.taken.ad.core.dto.web.oper.strategy.main.StrategyPageResp;
import cn.taken.ad.core.pojo.strategy.Strategy;

public interface StrategyDao extends BaseSuperDao<Strategy> {

    Page<StrategyPageResp> findPage(StrategyPageReq pageReq);

    void updateState(Integer state, Long id);

    void updateLastTime(Long id);

    Strategy findByMediaTagId(Long tagId);

    StrategyInfo findInfoById(Long id);

    StrategyInfo findInfoByTagId(Long id);
}
