package cn.taken.ad.core.dao.statistics;

import cn.taken.ad.component.orm.BaseSuperDao;
import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.constant.state.StatisticsType;
import cn.taken.ad.core.dto.web.collab.CollabUserDataAuthDto;
import cn.taken.ad.core.dto.web.oper.statistics.media.StatisticsMediaRequestExportReq;
import cn.taken.ad.core.dto.web.oper.statistics.media.StatisticsMediaRequestInfo;
import cn.taken.ad.core.dto.web.oper.statistics.media.StatisticsMediaRequestPageReq;
import cn.taken.ad.core.pojo.statistics.StatisticsMediaRequest;

import java.util.List;

public interface StatisticsMediaRequestDao extends BaseSuperDao<StatisticsMediaRequest> {

    Page<StatisticsMediaRequestInfo> findPage(StatisticsMediaRequestPageReq req);

    List<StatisticsMediaRequest> findShowList(StatisticsType statisticsType, String beginTime, String endTime, Long mediaId,Long mediaAppId,Long mediaTagId);

    List<StatisticsMediaRequest> findStatisticsMediaRequest(String beginTime, String endTime, StatisticsType statisticsType);

    void deleteByTime(StatisticsType type, String statisticsTime);

    List<StatisticsMediaRequestInfo> findExport(StatisticsMediaRequestExportReq req);

    Page<StatisticsMediaRequestInfo> findCooperatePage(StatisticsMediaRequestPageReq req, CollabUserDataAuthDto collabUserDataAuthDto);
}
