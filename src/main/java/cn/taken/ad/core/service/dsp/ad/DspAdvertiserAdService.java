package cn.taken.ad.core.service.dsp.ad;

import cn.taken.ad.component.utils.db.common.Page;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.core.dto.global.StatusInfo;
import cn.taken.ad.core.dto.web.oper.dsp.ad.*;
import cn.taken.ad.core.pojo.dsp.ad.DspAdvertiserAd;

import java.util.Date;
import java.util.List;

public interface DspAdvertiserAdService {
    Page<DspAdvertiserAdInfo> findPage(DspAdvertiserAdPageQuery pageReq);

    SuperResult<String> add(DspAdvertiserAdDto addReq, Long operatorId);

    SuperResult<String> modify(DspAdvertiserAdModify modifyReq, long operatorId);

    List<DspAdvertiserAd> findByLastUpdateTime(Date lastTime, int start, int limit);

    DspAdvertiserAdInfo findInfoById(Long id);

    SuperResult<String> remove(Long id, Long userId);

    List<DspAdvertiserAd> findList(DspAdvertiserAdListQuery req);

    void updateStatus(StatusInfo req);

    List<DspAdvertiserAd> findAllOpen(Long lastId, Integer size);

    void addBatch(List<DspAdvertiserAd> list);

    void updateCloseByIds(List<Long> needCloseIds);

    SuperResult<String> updateBatch(DspAdvertiserAdBatchModifyDto modifyDto);

    //重新打开已关停的当天的创意
    void updateOpenDayClosedIds(List<Long> openIds);

    void updateBatchStates(StatusInfo info);
}
