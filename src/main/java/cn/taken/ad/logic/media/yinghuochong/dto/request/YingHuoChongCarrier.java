package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongCarrier {
    Unknown(0),
    Mobile(1),
    Telecom(2),
    Unicom(3);

    private final int code;

    YingHuoChongCarrier(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongCarrier findByCode(int code) {
        for (YingHuoChongCarrier carrier : YingHuoChongCarrier.values()) {
            if (carrier.code == code) {
                return carrier;
            }
        }
        return Unknown;
    }
}
