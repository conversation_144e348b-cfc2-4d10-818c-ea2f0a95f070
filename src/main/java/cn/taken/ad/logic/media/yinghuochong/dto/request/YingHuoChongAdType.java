package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongAdType {
    FEED(1),
    SPLASH(2),
    RVIDEO(3),
    INSERT(4);

    private final int code;

    YingHuoChongAdType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongAdType findByCode(int code) {
        for (YingHuoChongAdType adType : YingHuoChongAdType.values()) {
            if (adType.code == code) {
                return adType;
            }
        }
        return FEED; // 默认返回信息流广告
    }
}
