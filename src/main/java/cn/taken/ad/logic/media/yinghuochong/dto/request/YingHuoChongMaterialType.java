package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongMaterialType {
    AdMaterialTypeImg(1),
    AdMaterialTypeVideo(2),
    AdMaterialTypeImgOrVideo(3);

    private final int code;

    YingHuoChongMaterialType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongMaterialType findByCode(int code) {
        for (YingHuoChongMaterialType materialType : YingHuoChongMaterialType.values()) {
            if (materialType.code == code) {
                return materialType;
            }
        }
        return AdMaterialTypeImgOrVideo; // 默认返回图片或视频
    }
}
