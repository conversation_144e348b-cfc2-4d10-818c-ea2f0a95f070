package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongDeviceType {
    UNKNOWN_DEVICE(0),
    PHONE(1),
    PAD(2);

    private final int code;

    YingHuoChongDeviceType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongDeviceType findByCode(int code) {
        for (YingHuoChongDeviceType deviceType : YingHuoChongDeviceType.values()) {
            if (deviceType.code == code) {
                return deviceType;
            }
        }
        return UNKNOWN_DEVICE;
    }
}
