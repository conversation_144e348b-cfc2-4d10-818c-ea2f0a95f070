package cn.taken.ad.logic.media.yinghuochong.dto.request;

public enum YingHuoChongNetworkType {
    UNKNOWN_NETWORK(0),
    WIFI(1),
    MOBILE_2G(2),
    MOBILE_3G(3),
    MOBILE_4G(4),
    MOBILE_5G(5);

    private final int code;

    YingHuoChongNetworkType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongNetworkType findByCode(int code) {
        for (YingHuoChongNetworkType networkType : YingHuoChongNetworkType.values()) {
            if (networkType.code == code) {
                return networkType;
            }
        }
        return UNKNOWN_NETWORK;
    }
}
