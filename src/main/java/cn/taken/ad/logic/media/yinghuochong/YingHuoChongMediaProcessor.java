package cn.taken.ad.logic.media.yinghuochong;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.compress.GzipUtils;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.configuration.cache.BaseRedisL2Cache;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.constant.redis.BaseRedisKeys;
import cn.taken.ad.core.pojo.media.Media;
import cn.taken.ad.core.pojo.media.MediaApp;
import cn.taken.ad.core.pojo.media.MediaTag;
import cn.taken.ad.logic.AbstractMediaProcessor;
import cn.taken.ad.logic.base.bill.RtbBillDto;
import cn.taken.ad.logic.base.event.RtbEventDto;
import cn.taken.ad.logic.base.rtb.RtbMediaDto;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseAppDto;
import cn.taken.ad.logic.base.rtb.response.dto.ResponseVideoDto;
import cn.taken.ad.logic.base.rtb.response.dto.TagResponseDto;
import cn.taken.ad.logic.media.yinghuochong.dto.request.*;
import cn.taken.ad.logic.media.yinghuochong.dto.response.*;
import cn.taken.ad.utils.web.HttpRequestUtils;
import cn.taken.ad.utils.web.HttpResponseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * https://alidocs.dingtalk.com/i/nodes/pYLaezmVNeql1jvMUa0vMQe7WrMqPxX6
 */
@Component("YINGHUOCHONG" + LogicSuffix.MEDIA_LOGIC_SUFFIX)
public class YingHuoChongMediaProcessor extends AbstractMediaProcessor {
    private static final Logger log = LoggerFactory.getLogger(YingHuoChongMediaProcessor.class);

    @Autowired
    private BaseRedisL2Cache baseRedisL2Cache;
    private static Map<String, String> installAppMap = new ConcurrentHashMap<>();

    static {
        installAppMap.put("com.taobao.taobao", "10001");
        installAppMap.put("com.tmall.wireless", "10002");
        installAppMap.put("com.sankuai.meituan", "10003");
        installAppMap.put("com.sankuai.meituan.takeoutnew", "10004");
        installAppMap.put("com.smile.gifmaker", "10005");
        installAppMap.put("com.kuaishou.nebula", "10006");
        installAppMap.put("com.eg.android.AlipayGphone", "10007");
        installAppMap.put("com.jingdong.app.mall", "10008");
        installAppMap.put("com.jd.jrapp", "10009");
        installAppMap.put("com.xunmeng.pinduoduo", "10010");
        installAppMap.put("com.achievo.vipshop", "10011");
        installAppMap.put("com.baidu.searchbox", "10012");
        installAppMap.put("com.taobao.idlefish", "10013");
        installAppMap.put("com.sdu.didi.psnger", "10014");
        installAppMap.put("ctrip.android.view", "10015");
        installAppMap.put("com.netease.yanxuan", "10016");
        installAppMap.put("com.homelink.android", "10017");
        installAppMap.put("com.dangdang.buy2", "10018");
        installAppMap.put("me.ele", "10019");
        installAppMap.put("com.oppo.store", "10020");
        installAppMap.put("com.vivo.browser", "10021");
        installAppMap.put("com.dianping.v1", "10022");
        installAppMap.put("com.shizhuang.duapp", "10023");
        installAppMap.put("com.youku.phone", "10024");
        installAppMap.put("com.qiyi.video", "10025");
        installAppMap.put("com.sina.weibo", "10026");
        installAppMap.put("com.tianyancha.skyeye", "10027");
        installAppMap.put("com.huawei.appmarket", "10028");
        installAppMap.put("air.tv.douyu.android", "10029");
        installAppMap.put("com.yek.android.kfc.activitys", "10030");
        installAppMap.put("com.tencent.qqlive", "10031");
        installAppMap.put("com.ss.android.ugc.aweme", "10032");
        installAppMap.put("com.lianjia.beike", "10033");
        installAppMap.put("com.tencent.news", "10034");
        installAppMap.put("com.zhihu.android", "10035");
        installAppMap.put("com.wuba", "10036");
        installAppMap.put("tv.danmaku.bili", "10037");
        installAppMap.put("com.ss.android.article.lite", "10038");
        installAppMap.put("com.tencent.qqmusic", "10039");
        installAppMap.put("com.anjuke.android.app", "10040");
        installAppMap.put("com.mfw.roadbook", "10041");
        installAppMap.put("com.Qunar", "10042");
        installAppMap.put("com.tongcheng.android", "10043");
        installAppMap.put("com.cubic.autohome", "10044");
        installAppMap.put("com.cmbchina.ccd.pluto.cmbActivity", "10045");
        installAppMap.put("com.taobao.trip", "10046");
        installAppMap.put("com.quark.browser", "10047");
        installAppMap.put("com.tencent.karaoke", "10048");
        installAppMap.put("com.kugou.android", "10049");
        installAppMap.put("com.netease.cloudmusic", "10050");
        installAppMap.put("com.tencent.map", "10051");
        installAppMap.put("com.tencent.mtt", "10052");
        installAppMap.put("com.autonavi.minimap", "10053");
        installAppMap.put("cmb.pb", "10054");
        installAppMap.put("com.t3go.passenger", "10055");
        installAppMap.put("com.jingyao.easybike", "10056");
        installAppMap.put("com.xiaolachuxing.user", "10057");
        installAppMap.put("com.UCMobile", "10058");
        installAppMap.put("com.xingin.xhs", "10059");
        installAppMap.put("com.ss.android.article.news", "10060");
        installAppMap.put("com.qiyi.video.lite", "10061");
        installAppMap.put("com.ss.android.article.video", "10062");
        installAppMap.put("com.taobao.litetao", "10063");
        installAppMap.put("com.taobao.live", "10064");
        installAppMap.put("com.ss.android.ugc.aweme.lite", "10065");
        installAppMap.put("com.alibaba.wireless", "10066");
        installAppMap.put("com.greenpoint.android.mc10086.activity", "10067");
        installAppMap.put("com.baidu.searchbox.lite", "10068");
        installAppMap.put("com.chinatelecom.bestpayclient", "10069");
        installAppMap.put("com.tencent.mm", "10070");
        installAppMap.put("com.taou.maimai", "10071");
        installAppMap.put("com.ecitic.bank.mobile", "10072");
        installAppMap.put("com.hunantv.imgo.activity", "10073");
        installAppMap.put("com.ucmobile.lite", "10074");
        installAppMap.put("com.cainiao.wireless", "10075");
        installAppMap.put("com.duowan.mobile", "10076");
        installAppMap.put("com.cmcc.cmvideo", "10077");
        installAppMap.put("com.jd.jdhealth", "10084");
    }

    @Override
    public RtbRequestDto parseRtb(RtbMediaDto mediaDto, String rtbId) throws Throwable {
        byte[] data = HttpRequestUtils.readBytes();
        if (data == null || data.length == 0) {
            log.info("RtbId:{},Code:{},Data Is Empty", mediaDto.getMediaCode(), rtbId);
            return null;
        }
        data = GzipUtils.decompress(data);
        AdRequest request = JsonHelper.fromJson(AdRequest.class,
                new String(data, StandardCharsets.UTF_8));
        mediaDto.setReqObj(request);
        if (StringUtils.isEmpty(request.getReqId())) {
            return null;
        }

        if (request.getAdSlots() == null || request.getAdSlots().isEmpty()) {
            return null;
        }
        AdSlot adSlot = request.getAdSlots().get(0);
        if (StringUtils.isEmpty(adSlot.getAdSlotId()) || StringUtils.isEmpty(adSlot.getAppId())) {
            return null;
        }

        MediaApp mediaApp = baseRedisL2Cache.get(BaseRedisKeys.KV_MEDIA_APP_CODE_ + mediaDto.getMediaId() + "_" + adSlot.getAppId(), MediaApp.class);
        if (mediaApp == null) {
            return null;
        }

        MediaTag mediaTag = baseRedisL2Cache.get(
                BaseRedisKeys.KV_MEDIA_TAG_CODE_ + mediaDto.getMediaId() + "_" + mediaApp.getId() + "_" + adSlot.getAdSlotId(),
                MediaTag.class);
        if (mediaTag == null) {
            return null;
        }
        RtbRequestDto requestDto = new RtbRequestDto();
        requestDto.setReqId(request.getReqId());
        // tag
        requestDto.setTag(createReqTag(adSlot, mediaTag));
        // app
        if (request.getApp() != null) {
            App app = request.getApp();
            requestDto.setApp(createReqApp(app, mediaApp.getCode()));
        }
        // deivce
        requestDto.setDevice(createReqDevice(request));
        // network
        requestDto.setNetwork(createReqNetwork(request));
        // geo
        if (request.getUserLocation() != null) {
            RequestGeoDto geoDto = new RequestGeoDto();
            geoDto.setLatitude(request.getUserLocation().getLat().doubleValue());
            geoDto.setLongitude(request.getUserLocation().getLon().doubleValue());
            geoDto.setCoordinateType(CoordinateType.GLOBAL);
            requestDto.setGeo(geoDto);
        }
        log.info("request:" + JsonHelper.toJsonString(requestDto));
        return requestDto;
    }


    private RequestNetworkDto createReqNetwork(AdRequest request) {
        RequestNetworkDto builder = new RequestNetworkDto();
        builder.setIp(request.getIp());
        if (StringUtils.isNotBlank(request.getIpv6())) {
            builder.setIpv6(request.getIpv6());
        }

        // 从Device对象中获取网络相关信息
        Device device = request.getDevice();
        if (device != null) {
            // 网络类型映射
            if (device.getNetworkType() != null) {
                switch (device.getNetworkType()) {
                    case WIFI:
                        builder.setConnectType(ConnectionType.WIFI);
                        break;
                    case MOBILE_2G:
                        builder.setConnectType(ConnectionType.NETWORK_2G);
                        break;
                    case MOBILE_3G:
                        builder.setConnectType(ConnectionType.NETWORK_3G);
                        break;
                    case MOBILE_4G:
                        builder.setConnectType(ConnectionType.NETWORK_4G);
                        break;
                    case MOBILE_5G:
                        builder.setConnectType(ConnectionType.NETWORK_5G);
                        break;
                    case UNKNOWN_NETWORK:
                    default:
                        builder.setConnectType(ConnectionType.UNKNOWN);
                        break;
                }
            }

            // 运营商映射
            if (device.getCarrier() != null) {
                switch (device.getCarrier()) {
                    case Mobile:
                        builder.setCarrierType(CarrierType.CM);
                        break;
                    case Telecom:
                        builder.setCarrierType(CarrierType.CT);
                        break;
                    case Unicom:
                        builder.setCarrierType(CarrierType.CU);
                        break;
                    case Unknown:
                    default:
                        builder.setCarrierType(CarrierType.UNKNOWN);
                        break;
                }
            }
        }

        return builder;
    }

    private RequestDeviceDto createReqDevice(AdRequest request) {
        Device device = request.getDevice();
        RequestDeviceDto builder = new RequestDeviceDto();

        // 添加空值检查
        if (device == null) {
            return builder;
        }

        // User Agent
        if (StringUtils.isNotBlank(device.getUa())) {
            builder.setUserAgent(device.getUa());
        }

        // 设备类型映射
        if (device.getDeviceType() != null) {
            switch (device.getDeviceType()) {
                case PHONE:
                    builder.setDeviceType(DeviceType.PHONE);
                    break;
                case PAD:
                    builder.setDeviceType(DeviceType.PAD);
                    break;
                case UNKNOWN_DEVICE:
                default:
                    builder.setDeviceType(DeviceType.UNKNOWN);
                    break;
            }
        } else {
            builder.setDeviceType(DeviceType.UNKNOWN);
        }

        // 操作系统映射
        if (device.getOs() != null) {
            switch (device.getOs()) {
                case ANDROID:
                    builder.setOsType(OsType.ANDROID);
                    break;
                case IOS:
                    builder.setOsType(OsType.IOS);
                    break;
                case HARMONY:
                    builder.setOsType(OsType.HARMONY);
                    break;
                case WINDOWS:
                    builder.setOsType(OsType.WINDOWS_PC);
                    break;
                case UNKNOWN_OS:
                default:
                    builder.setOsType(OsType.UNKNOWN);
                    break;
            }
        } else {
            builder.setOsType(OsType.UNKNOWN);
        }

        // 操作系统版本
        if (StringUtils.isNotBlank(device.getOsv())) {
            builder.setOsVersion(device.getOsv());
        }

        // 设备厂商
        if (StringUtils.isNotBlank(device.getMake())) {
            builder.setVendor(device.getMake());
        }

        // 设备型号
        if (StringUtils.isNotBlank(device.getModel())) {
            builder.setModel(device.getModel());
        }

        // 设备品牌
        if (StringUtils.isNotBlank(device.getBrand())) {
            builder.setBrand(device.getBrand());
        }

        // 屏幕尺寸
        if (device.getScreenWidth() != null) {
            builder.setWidth(device.getScreenWidth());
        }
        if (device.getScreenHeight() != null) {
            builder.setHeight(device.getScreenHeight());
        }

        // 处理Uid对象（移动设备序列号标识字段）
        if (device.getUid() != null) {
            Uid uid = device.getUid();

            // dpid (明文Android Id)
            if (StringUtils.isNotBlank(uid.getDpid())) {
                builder.setAndroidId(uid.getDpid());
            }

            // dpidMd5 (Android Id Md5)
            if (StringUtils.isNotBlank(uid.getDpidMd5())) {
                builder.setAndroidIdMd5(uid.getDpidMd5());
            }

            // oaid (必填字段)
            if (StringUtils.isNotBlank(uid.getOaid())) {
                builder.setOaid(uid.getOaid());
            }

            // oaidMd5 (oaid Md5)
            if (StringUtils.isNotBlank(uid.getOaidMd5())) {
                builder.setOaidMd5(uid.getOaidMd5());
            }
        }

        // 已安装的APP信息
        if (!CollectionUtils.isEmpty(device.getPkgList())) {
            List<RequestInstalledAppDto> appDtos = new LinkedList<>();
            device.getPkgList().forEach(code -> {
                if (StringUtils.isNotBlank(code)) {
                    RequestInstalledAppDto dto = new RequestInstalledAppDto();
                    // 根据萤火虫文档，pkgList中的code是编码，需要找到对应的包名
                    String packageName = null;
                    for (Map.Entry<String, String> entry : installAppMap.entrySet()) {
                        if (entry.getValue().equals(code)) {
                            packageName = entry.getKey();
                            break;
                        }
                    }
                    if (StringUtils.isNotBlank(packageName)) {
                        dto.setPackageName(packageName);
                        appDtos.add(dto);
                    }
                }
            });
            if (!appDtos.isEmpty()) {
                builder.setInstalledAppInfo(appDtos);
            }
        }

        return builder;
    }

    private RequestAppDto createReqApp(App app, String mediaAppCode) {
        RequestAppDto builder = new RequestAppDto();
        builder.setAppId(mediaAppCode);
        if (StringUtils.isNotBlank(app.getName())) {
            builder.setAppName(app.getName());
        }
        if (StringUtils.isNotBlank(app.getVersion())) {
            builder.setAppVersion(app.getVersion());
        }
        if (StringUtils.isNotBlank(app.getBundle())) {
            builder.setBundle(app.getBundle());
        }
        return builder;
    }

    private RequestTagDto createReqTag(AdSlot adSlot, MediaTag mediaTag) {
        RequestTagDto builder = new RequestTagDto();
        builder.setTagId(adSlot.getAdSlotId());
        // * 类型
        if (YingHuoChongAdType.FEED == adSlot.getAdType()) {
            builder.setTagType(TagType.INFORMATION_FLOW);
        } else if (YingHuoChongAdType.RVIDEO == adSlot.getAdType()) {
            builder.setTagType(TagType.INCENTIVE_VIDEO);
        } else if (YingHuoChongAdType.SPLASH == adSlot.getAdType()) {
            builder.setTagType(TagType.OPEN);
        } else if (YingHuoChongAdType.INSERT == adSlot.getAdType()) {
            builder.setTagType(TagType.INTERSTITIAL);
        } else {
            builder.setTagType(TagType.OTHER);
        }
        builder.setSize(1);
        if (adSlot.getBidFloor() != null) {
            builder.setPrice(Double.parseDouble(adSlot.getBidFloor().toString()));
        }
        if (adSlot.getAdInfo() != null) {
            AdInfo adInfo = adSlot.getAdInfo();

            if (adInfo.getVideoInfo() != null) {
                builder.setMaxDuration(adInfo.getVideoInfo().getDuration());
            }
//            todo
//            if (adSlot.getSlot_width() != null) {
//                builder.setWidth(request.getSlot_width());
//            }
//            if (request.getSlot_height() != null) {
//                builder.setHeight(request.getSlot_height());
//            }
//            if (request.getSecure() != null) {
//                builder.setNeedHttps(request.getSecure() == 1);
//            }
        }

        return builder;
    }

    @Override
    public SuperResult<String> returnRtb(RtbMediaDto mediaDto) throws Throwable {
        RtbResponseDto resp = mediaDto.getResponse();
        AdRequest request = (AdRequest) mediaDto.getReqObj();
        AdResponse response = new AdResponse();

        // 设置reqId
        if (request != null && StringUtils.isNotBlank(request.getReqId())) {
            response.setReqId(request.getReqId());
        }

        LogicState state = LogicState.findByCode(resp.getCode());
        if (state != LogicState.SUCCESS) {
            response.setNbr(-1);
            response.setMsg("失败");
        } else if (null == resp.getTags() || resp.getTags().isEmpty()) {
            response.setNbr(100005);
            response.setMsg("缺少广告位信息");
        } else {
            response.setNbr(0);
            response.setMsg("成功");

            // 创建SeatBid对象
            List<SeatBid> seatBids = new ArrayList<>();
            SeatBid seatBid = new SeatBid();
            seatBid.setSeat("firefly");

            List<Bid> bidList = new ArrayList<>();

            resp.getTags().forEach(tag -> {
                Bid ad = new Bid();
                ad.setId(tag.getTagInfoId());
                ad.setAdId(tag.getTagInfoId());
                ad.setAdSlotId(mediaDto.getRequest().getTag().getTagId());
                ad.setCreateId(tag.getCreativeId());
                ad.setAdm(buildAdm(mediaDto, tag));
                ad.setNurl(tag.getWinNoticeUrls());
                ad.setLurl(tag.getFailNoticeUrls());

                // 根据请求的报价类型设置价格
                if (request != null && request.getReportPriceType() == YingHuoChongReportPriceType.CPC) {
                    ad.setReportPriceType(ReportPriceType.CPC);
                    if (tag.getRespMediaPrice() != null) {
                        // CPC价格单位为元/每次点击，转换为float
                        ad.setCpcPrice(tag.getRespMediaPrice().floatValue() / 100.0f);
                    }
                } else {
                    ad.setReportPriceType(ReportPriceType.CPM);
                    if (tag.getRespMediaPrice() != null) {
                        ad.setPrice(tag.getRespMediaPrice().longValue());
                    }
                }

                List<TrackMonitor> monitors = new ArrayList<>();

                if (!CollectionUtils.isEmpty(tag.getTracks())) {
                    tag.getTracks().forEach(track -> {
                        List<String> urls = track.getTrackUrls();

                        // 根据报价类型替换价格宏
                        if (request != null && request.getReportPriceType() == YingHuoChongReportPriceType.CPC) {
                            urls = replaceMacro("${__CPC_AUCTION_PRICE__}", urls, MacroType.WIN_PRICE.getCode());
                        } else {
                            urls = replaceMacro("${__AUCTION_PRICE__}", urls, MacroType.WIN_PRICE.getCode());
                        }
                        int type = track.getTrackType();

                        if (type == EventType.EXPOSURE.getType()) {
                            ad.setShowUrl(urls);
                        } else if (type == EventType.CLICK.getType()) {
                            ad.setClickUrl(urls);
                        }

                        TrackMonitor monitor = new TrackMonitor();
                        switch (type) {
                            case 15:
                                monitor.setEvent(TrackEvent.VIDEO_START);
                                break;
                            case 21:
                                monitor.setEvent(TrackEvent.VIDEO_CLOSE);
                                break;
                            case 24:
                                monitor.setEvent(TrackEvent.VIDEO_READY_PLAY);
                                break;
                            case 29:
                                monitor.setEvent(TrackEvent.VIDEO_CONTINUE_PLAY);
                                break;
                            case 28:
                                monitor.setEvent(TrackEvent.VIDEO_PAUSE);
                                break;
                            case 19:
                                monitor.setEvent(TrackEvent.VIDEO_PLAY_END);
                                break;
                            case 31:
                                monitor.setEvent(TrackEvent.VIDEO_REPEATED_PLAY);
                                break;
                            case 20:
                                monitor.setEvent(TrackEvent.SKIP);
                                break;
                            case 25:
                                monitor.setEvent(TrackEvent.VIDEO_PLAY_FAIL);
                                break;
                            case 26:
                            case 27:
                                monitor.setEvent(TrackEvent.VIDEO_TURN_ON_OFF_SOUND_BUTTON);
                                break;
                        }
                        monitor.setUrl(track.getTrackUrls());
                        monitors.add(monitor);
                    });
                }

                ad.setMons(monitors);
                bidList.add(ad);
            });

            seatBid.setBid(bidList);
            seatBids.add(seatBid);
            response.setSeatBid(seatBids);
        }

        mediaDto.setRespObj(response);
        HttpResponseUtils.outputJson(response);
        return SuperResult.rightResult();
    }

    private static Adm buildAdm(RtbMediaDto mediaDto, TagResponseDto tag) {
        Adm adm = new Adm();
        // 广告交互类型
        if (ActionType.WEB_VIEW_H5 == tag.getActionType()
                || ActionType.SYSTEM_BROWSER_H5 == tag.getActionType()) {
            adm.setActionType(YingHuoChongActionType.LANDINGPAGE);
        } else if (ActionType.DOWNLOAD == tag.getActionType()) {
            adm.setActionType(YingHuoChongActionType.DOWNLOAD);
        } else if (ActionType.DEEPLINK == tag.getActionType()) {
            adm.setActionType(YingHuoChongActionType.DEEPLINK);
        } else if (ActionType.MINI_PROGRAM == tag.getActionType()) {
            adm.setActionType(YingHuoChongActionType.LANDINGPAGE);
        } else {
            adm.setActionType(YingHuoChongActionType.LANDINGPAGE);
        }

        switch (mediaDto.getRequest().getTag().getTagType()) {
            case OPEN:
                adm.setAdType(YingHuoChongAdType.SPLASH);
                break;
            case INFORMATION_FLOW:
                adm.setAdType(YingHuoChongAdType.FEED);
                break;
            case INCENTIVE_VIDEO:
                adm.setAdType(YingHuoChongAdType.RVIDEO);
                break;
            case INTERSTITIAL:
                adm.setAdType(YingHuoChongAdType.INSERT);
                break;
            default:
                adm.setAdType(YingHuoChongAdType.FEED);
        }

        //返回物料类型 1图片 2视频
        switch (tag.getMaterialType()) {
            case VIDEO:
                adm.setMaterialType(2);
                break;
            case IMAGE_TEXT:
                adm.setMaterialType(1);
                break;
            case TEXT:
                adm.setMaterialType(1);
                break;
            case HTML:
                adm.setMaterialType(1);
                break;
        }

        // 设置基本信息
        if (StringUtils.isNotBlank(tag.getIconUrl())) {
            adm.setIcon(tag.getIconUrl());
        }
        if (StringUtils.isNotBlank(tag.getDesc())) {
            adm.setDesc(tag.getDesc());
        }
        if (StringUtils.isNotBlank(tag.getTitle())) {
            adm.setTitle(tag.getTitle());
        }
        if (StringUtils.isNotBlank(tag.getDeepLinkUrl())) {
            adm.setDeeplink(tag.getDeepLinkUrl());
        }
        if (tag.getImgUrls() != null && !tag.getImgUrls().isEmpty()) {
            List<Image> imgInfo = new ArrayList<>();
            tag.getImgUrls().forEach(imgUrl -> {
                Image image = new Image();
                image.setUrl(imgUrl);
                image.setWidth(tag.getMaterialWidth());
                image.setHeight(tag.getMaterialHeight());
                image.setDescription(tag.getDesc());
                imgInfo.add(image);
            });
            adm.setImgInfo(imgInfo);
        }
        if (tag.getVideoInfo() != null) {
            ResponseVideoDto videoDto = tag.getVideoInfo();
            Video video = new Video();
            if (StringUtils.isNotBlank(videoDto.getVideoUrl())) {
                video.setUrl(videoDto.getVideoUrl());
            }
            if (videoDto.getDuration() != null) {
                video.setDuration(videoDto.getDuration());
            }
            if (videoDto.getVideoWidth() != null && videoDto.getVideoWidth() > 0) {
                video.setWidth(videoDto.getVideoWidth());
            }
            if (videoDto.getVideoHeight() != null && videoDto.getVideoHeight() > 0) {
                video.setHeight(videoDto.getVideoHeight());
            }
            if (videoDto.getVideoSize() != null && videoDto.getVideoSize() > 0) {
                video.setSize(videoDto.getVideoSize().intValue());
            }
            if (!CollectionUtils.isEmpty(videoDto.getCoverImgUrls())) {
                video.setCoverUrl(videoDto.getCoverImgUrls().get(0));
            }
            if (videoDto.getCoverWidth() != null && videoDto.getCoverWidth() > 0) {
                video.setCoverWidth(videoDto.getCoverWidth());
            }
            if (videoDto.getCoverHeight() != null && videoDto.getCoverHeight() > 0) {
                video.setCoverHeight(videoDto.getCoverHeight());
            }

            if (StringUtils.isNotBlank(videoDto.getResolution())) {
                try {
                    video.setRatio(Integer.parseInt(videoDto.getResolution()));
                } catch (Exception e) {
                }
            }
            adm.setVideoInfo(video);
        }


        if (tag.getAppInfo() != null) {
            ResponseAppDto appDto = tag.getAppInfo();
            AndroidApp app = new AndroidApp();
            if (StringUtils.isNotBlank(appDto.getAppName())) {
                app.setAppName(appDto.getAppName());
            }
            if (StringUtils.isNotBlank(appDto.getPackageName())) {
                app.setPackageName(appDto.getPackageName());
            }

            if (appDto.getAppSize() != null && appDto.getAppSize() > 0) {
                app.setPackageSize(appDto.getAppSize().intValue());
            }
            if (StringUtils.isNotBlank(appDto.getAppVersion())) {
                app.setAppVersion(appDto.getAppVersion());
            }

            // 下载链接
            if (StringUtils.isNotBlank(tag.getMarketUrl())) {
                app.setDownloadUrl(tag.getMarketUrl());
            }

            // 应用市场链接
            if (StringUtils.isNotBlank(appDto.getAppInfoUrl())) {
                app.setMarketUrl(appDto.getAppInfoUrl());
                app.setAppIntroductionLink(appDto.getAppInfoUrl());
            }

            // 应用图标
            if (StringUtils.isNotBlank(appDto.getAppIconUrl())) {
                app.setOpenUrl(appDto.getAppIconUrl());
            }
            if (StringUtils.isNotBlank(appDto.getAppDeveloper())) {
                app.setPublisher(appDto.getAppDeveloper());
            }
            if (StringUtils.isNotBlank(appDto.getAppPrivacyUrl())) {
                app.setPrivacy(appDto.getAppPrivacyUrl());
            }
            if (StringUtils.isNotBlank(appDto.getAppPermissionInfoUrl())) {
                app.setPermission(appDto.getAppPermissionInfoUrl());
            }
            if (StringUtils.isNotBlank(appDto.getRecordNumber())) {
                app.setRegNumber(appDto.getRecordNumber());
            }
            adm.setAndroidApp(app);
        }

        // LandingPage是必填字段
        LandingPage landingPage = new LandingPage();
        if (StringUtils.isNotBlank(tag.getClickUrl())) {
            landingPage.setUrl(tag.getClickUrl());
        }
        if (StringUtils.isNotBlank(tag.getUniversalLink())) {
            landingPage.setLpOpenUrl(tag.getUniversalLink());
        }
        adm.setLandingPage(landingPage);

        return adm;
    }

    private List<String> replaceMacro(String macro, List<String> urls, String msg) {
        return urls.stream().map(url -> url.replace(macro, msg)).collect(Collectors.toList());
    }

    @Override
    public SuperResult<String> mediaParseBill(RtbBillDto bill, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<String> mediaParseEvent(RtbEventDto event, Media media) {
        return SuperResult.rightResult();
    }

    @Override
    public SuperResult<Double> decryptPrice(String price, Media media) {
        if (StringUtils.isBlank(price)) {
            return SuperResult.badResult("price empty");
        }
        if (price.equals("__PRICE__") || price.equals("${__AUCTION_PRICE__}") || price.equals("${__CPC_AUCTION_PRICE__}")) {
            // 未替换 价格宏
            return SuperResult.badResult("price not replaced");
        }
        try {
            String priceSecretKey = media.getPriceKey();
            String data = aesDecrypt(price, priceSecretKey);
            return SuperResult.rightResult(Double.valueOf(data));
        } catch (Exception e) {
            return SuperResult.badResult("decrypt[" + price + "] error:" + e.getMessage());
        }
    }

    /**
     * AES ECB SafeUrl 解密
     */
    public static String aesDecrypt(String encode, String key) throws Exception {
        byte[] encrypted = safeUrlBase64Decode(encode);
        byte[] enCodeFormat = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(enCodeFormat, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] original = cipher.doFinal(encrypted);
        return new String(original, StandardCharsets.UTF_8);
    }

    /**
     * 加解密价格放在http请求中，避免出现http中关键字报错，替换掉关键字
     */
    private static byte[] safeUrlBase64Decode(final String safeBase64Str) {
        String base64Str = safeBase64Str.replace('-', '+');
        base64Str = base64Str.replace('_', '/');
        int mod4 = base64Str.length() % 4;
        if (mod4 > 0) {
            base64Str = base64Str + "====".substring(mod4);
        }
        return Base64.getDecoder().decode(base64Str);
    }

}
