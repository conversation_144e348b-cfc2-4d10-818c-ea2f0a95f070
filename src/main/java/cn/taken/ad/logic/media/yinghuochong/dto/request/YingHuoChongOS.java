package cn.taken.ad.logic.media.yinghuochong.dto.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum YingHuoChongOS {
    UNKNOWN_OS(0),
    IOS(1),
    ANDROID(2),
    WINDOWS(3),
    HARMONY(4);

    private final int code;

    YingHuoChongOS(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static YingHuoChongOS findByCode(int code) {
        for (YingHuoChongOS os : YingHuoChongOS.values()) {
            if (os.code == code) {
                return os;
            }
        }
        return UNKNOWN_OS;
    }
}
