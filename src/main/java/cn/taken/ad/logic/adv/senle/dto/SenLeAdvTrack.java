package cn.taken.ad.logic.adv.senle.dto;

import java.io.Serializable;
import java.util.List;

public class SenLeAdvTrack implements Serializable {

    private static final long serialVersionUID = 2246442881578354927L;
    /**
     * 视频跟踪事件类型，1: 开始播放; 2:播放至 25%时; 3:播放至 50% 时;4:播放至 75%时; 5:播放完成 6:视频静音; 7:视频全屏; 8:视频跳过;9:视频关闭
     */
    private Integer ttype;
    /**
     * 上报链接，不为空则遍历逐一上报
     */
    private List<String> urls;

    public Integer getTtype() {
        return ttype;
    }

    public void setTtype(Integer ttype) {
        this.ttype = ttype;
    }

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }
}
