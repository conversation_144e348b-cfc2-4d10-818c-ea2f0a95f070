package cn.taken.ad.logic.adv.yiqi.dto;

import java.util.List;

public class YiQiResponse {
    /**
     * 0 success，请求成功。
     * 204 未找到广告
     * 10001 请求参数错误
     * 10002 广告位错误
     * 10003 广告位设置错误
     * 10004 其它错误
     */
    private Integer code;
    private String msg;
    private List<YiQiResponseAds> ads;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<YiQiResponseAds> getAds() {
        return ads;
    }

    public void setAds(List<YiQiResponseAds> ads) {
        this.ads = ads;
    }
}
