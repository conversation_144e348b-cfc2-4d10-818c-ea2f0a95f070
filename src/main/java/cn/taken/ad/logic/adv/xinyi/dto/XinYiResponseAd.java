package cn.taken.ad.logic.adv.xinyi.dto;

import java.io.Serializable;
import java.util.List;

public class XinYiResponseAd implements Serializable {
    private static final long serialVersionUID = -3438702973194911374L;
    private Integer width; //广告宽度
    private Integer height; //广告高度
    private String ad_id; //广告 id
    private String creative_id; //素材 id
    private Float price; //广告价格, 单位 分
    private String title; //标题
    private String subtitle; //副标题
    private String description; //描述
    private String advertiser_name; //广告主名称
    private String ratings; //评价数
    private String button_text; //按钮文字
    private String likes; //点赞次数
    private String downloads; //下载次数
    private List<XinYiImage> images; //图片数组
    private XinYiImage icon; //图标
    private XinYiImage logo; //logo
    private XinYiVideo video; //视频
    private XinYiImage video_cover; //视频封面图
    private String html_snippet; //html 代码片段, 在 webview 中加载
    private String html_url; //html 代码地址, 在 webview 中加载
    private Integer action; // 广告动作类型 1: 在应用内用 webview 打开 target_url 2: 在系统浏览器打开 target_url 6: 下载应用 7: 打开 deeplink_url, 如果失败则打开 target_url 8: 打开小程序
    private String target_url; //目标地址, 里面的宏会被替换
    private String download_app_bundle; //下载应用包名
    private String download_app_name; //下载应用名称
    private String download_app_version; //下载应用版本
    private Integer download_app_size; //下载应用大小, 单位 KB
    private String download_app_desc; //下载应用描述
    private String privacy_url; //下载应用隐私协议地址
    private String permission_url; //下载应用权限列表地址
    private String mini_program_id; //小程序原始id
    private String mini_program_path; //小程序页面路径
    private String deeplink_url; // 里面的宏会被替换
    private String win_notice_tracker; //竞价获胜通知地址

    private List<String> impression_trackers; //曝光, 里面的宏会被替换
    private List<String> click_trackers; //点击, 里面的宏会被替换
    private List<String> download_begin_trackers; //应用下载开始, 里面的宏会被替换
    private List<String> download_ended_trackers; //应用下载完成, 里面的宏会被替换
    private List<String> install_begin_trackers; //应用安装开始, 里面的宏会被替换
    private List<String> install_ended_trackers; //应用安装完成, 里面的宏会被替换
    private List<String> video_play_begin_trackers; //视频播放开始, 里面的宏会被替换
    private List<String> video_play_break_trackers; //视频播放中止, 里面的宏会被替换
    private List<String> video_play_ended_trackers; //视频播放结束, 里面的宏会被替换
    private List<String> deeplink_app_not_installed_trackers; //deeplink 唤醒时，发现应用没有安装
    private List<String> deeplink_app_installed_trackers; //deeplink 唤醒时，发现应用有安装
    private List<String> deeplink_app_invoke_failed_trackers; //deeplink 唤醒时，应用有安装但唤起失败
    private List<String> deeplink_app_invoke_success_trackers; //deeplink 唤醒时，应用唤起成功

    private String click_area_report_url; //汇川预算 [点击坐标打点 click_area_report_url](#点击坐标打点 click_area_report_url)



    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getAd_id() {
        return ad_id;
    }

    public void setAd_id(String ad_id) {
        this.ad_id = ad_id;
    }

    public String getCreative_id() {
        return creative_id;
    }

    public void setCreative_id(String creative_id) {
        this.creative_id = creative_id;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAdvertiser_name() {
        return advertiser_name;
    }

    public void setAdvertiser_name(String advertiser_name) {
        this.advertiser_name = advertiser_name;
    }

    public String getRatings() {
        return ratings;
    }

    public void setRatings(String ratings) {
        this.ratings = ratings;
    }

    public String getButton_text() {
        return button_text;
    }

    public void setButton_text(String button_text) {
        this.button_text = button_text;
    }

    public String getLikes() {
        return likes;
    }

    public void setLikes(String likes) {
        this.likes = likes;
    }

    public String getDownloads() {
        return downloads;
    }

    public void setDownloads(String downloads) {
        this.downloads = downloads;
    }

    public List<XinYiImage> getImages() {
        return images;
    }

    public void setImages(List<XinYiImage> images) {
        this.images = images;
    }

    public XinYiImage getIcon() {
        return icon;
    }

    public void setIcon(XinYiImage icon) {
        this.icon = icon;
    }

    public XinYiImage getLogo() {
        return logo;
    }

    public void setLogo(XinYiImage logo) {
        this.logo = logo;
    }

    public XinYiVideo getVideo() {
        return video;
    }

    public void setVideo(XinYiVideo video) {
        this.video = video;
    }

    public XinYiImage getVideo_cover() {
        return video_cover;
    }

    public void setVideo_cover(XinYiImage video_cover) {
        this.video_cover = video_cover;
    }

    public String getHtml_snippet() {
        return html_snippet;
    }

    public void setHtml_snippet(String html_snippet) {
        this.html_snippet = html_snippet;
    }

    public String getHtml_url() {
        return html_url;
    }

    public void setHtml_url(String html_url) {
        this.html_url = html_url;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getTarget_url() {
        return target_url;
    }

    public void setTarget_url(String target_url) {
        this.target_url = target_url;
    }

    public String getDownload_app_bundle() {
        return download_app_bundle;
    }

    public void setDownload_app_bundle(String download_app_bundle) {
        this.download_app_bundle = download_app_bundle;
    }

    public String getDownload_app_name() {
        return download_app_name;
    }

    public void setDownload_app_name(String download_app_name) {
        this.download_app_name = download_app_name;
    }

    public String getDownload_app_version() {
        return download_app_version;
    }

    public void setDownload_app_version(String download_app_version) {
        this.download_app_version = download_app_version;
    }

    public Integer getDownload_app_size() {
        return download_app_size;
    }

    public void setDownload_app_size(Integer download_app_size) {
        this.download_app_size = download_app_size;
    }

    public String getDownload_app_desc() {
        return download_app_desc;
    }

    public void setDownload_app_desc(String download_app_desc) {
        this.download_app_desc = download_app_desc;
    }

    public String getPrivacy_url() {
        return privacy_url;
    }

    public void setPrivacy_url(String privacy_url) {
        this.privacy_url = privacy_url;
    }

    public String getPermission_url() {
        return permission_url;
    }

    public void setPermission_url(String permission_url) {
        this.permission_url = permission_url;
    }

    public String getMini_program_id() {
        return mini_program_id;
    }

    public void setMini_program_id(String mini_program_id) {
        this.mini_program_id = mini_program_id;
    }

    public String getMini_program_path() {
        return mini_program_path;
    }

    public void setMini_program_path(String mini_program_path) {
        this.mini_program_path = mini_program_path;
    }

    public String getDeeplink_url() {
        return deeplink_url;
    }

    public void setDeeplink_url(String deeplink_url) {
        this.deeplink_url = deeplink_url;
    }

    public List<String> getImpression_trackers() {
        return impression_trackers;
    }

    public void setImpression_trackers(List<String> impression_trackers) {
        this.impression_trackers = impression_trackers;
    }

    public List<String> getClick_trackers() {
        return click_trackers;
    }

    public void setClick_trackers(List<String> click_trackers) {
        this.click_trackers = click_trackers;
    }

    public List<String> getDownload_begin_trackers() {
        return download_begin_trackers;
    }

    public void setDownload_begin_trackers(List<String> download_begin_trackers) {
        this.download_begin_trackers = download_begin_trackers;
    }

    public List<String> getDownload_ended_trackers() {
        return download_ended_trackers;
    }

    public void setDownload_ended_trackers(List<String> download_ended_trackers) {
        this.download_ended_trackers = download_ended_trackers;
    }

    public List<String> getInstall_begin_trackers() {
        return install_begin_trackers;
    }

    public void setInstall_begin_trackers(List<String> install_begin_trackers) {
        this.install_begin_trackers = install_begin_trackers;
    }

    public List<String> getInstall_ended_trackers() {
        return install_ended_trackers;
    }

    public void setInstall_ended_trackers(List<String> install_ended_trackers) {
        this.install_ended_trackers = install_ended_trackers;
    }

    public List<String> getVideo_play_begin_trackers() {
        return video_play_begin_trackers;
    }

    public void setVideo_play_begin_trackers(List<String> video_play_begin_trackers) {
        this.video_play_begin_trackers = video_play_begin_trackers;
    }

    public List<String> getVideo_play_break_trackers() {
        return video_play_break_trackers;
    }

    public void setVideo_play_break_trackers(List<String> video_play_break_trackers) {
        this.video_play_break_trackers = video_play_break_trackers;
    }

    public List<String> getVideo_play_ended_trackers() {
        return video_play_ended_trackers;
    }

    public void setVideo_play_ended_trackers(List<String> video_play_ended_trackers) {
        this.video_play_ended_trackers = video_play_ended_trackers;
    }

    public List<String> getDeeplink_app_not_installed_trackers() {
        return deeplink_app_not_installed_trackers;
    }

    public void setDeeplink_app_not_installed_trackers(List<String> deeplink_app_not_installed_trackers) {
        this.deeplink_app_not_installed_trackers = deeplink_app_not_installed_trackers;
    }

    public List<String> getDeeplink_app_installed_trackers() {
        return deeplink_app_installed_trackers;
    }

    public void setDeeplink_app_installed_trackers(List<String> deeplink_app_installed_trackers) {
        this.deeplink_app_installed_trackers = deeplink_app_installed_trackers;
    }

    public List<String> getDeeplink_app_invoke_failed_trackers() {
        return deeplink_app_invoke_failed_trackers;
    }

    public void setDeeplink_app_invoke_failed_trackers(List<String> deeplink_app_invoke_failed_trackers) {
        this.deeplink_app_invoke_failed_trackers = deeplink_app_invoke_failed_trackers;
    }

    public List<String> getDeeplink_app_invoke_success_trackers() {
        return deeplink_app_invoke_success_trackers;
    }

    public void setDeeplink_app_invoke_success_trackers(List<String> deeplink_app_invoke_success_trackers) {
        this.deeplink_app_invoke_success_trackers = deeplink_app_invoke_success_trackers;
    }

    public String getWin_notice_tracker() {
        return win_notice_tracker;
    }

    public void setWin_notice_tracker(String win_notice_tracker) {
        this.win_notice_tracker = win_notice_tracker;
    }

    public String getClick_area_report_url() {
        return click_area_report_url;
    }

    public void setClick_area_report_url(String click_area_report_url) {
        this.click_area_report_url = click_area_report_url;
    }
}
