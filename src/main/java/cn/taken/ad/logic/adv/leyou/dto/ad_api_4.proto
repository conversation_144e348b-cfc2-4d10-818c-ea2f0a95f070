syntax = "proto3";

// 指定Java文件包路径
option java_package="cn.taken.ad.logic.adv.leyou.dto";
// 是否拆分为多个Java类
option java_multiple_files = true;
// java文件名称
option java_outer_classname = "LeYouAdvPeqRespDto";


message Request {
    string uuid = 1; //预留,暂未使用
    string push_version = 2;//预留,暂未使用
    string data = 3;//预留,暂未使用
    string api_version = 4;//接口版本号，请按文档版本号填写，版本号可能会影响返回内容
    string api_type = 5;//接入方式（大写），API:服务器对接(默认) SDK:客户端对接
    string request_id = 6;//请求 id，媒体侧生成，需确保全局唯一，最大长度为 36 位
    string ua = 7;//客户端的 User Agent。必须是客户端通过系统 API 获取的 真实 UA，不能自定义
    AdSlot ad_slot = 8;
    App app = 9;
    Device device = 10;
    Network network = 11;
    optional Geo geo = 12;
    optional User user = 13;

    message AdSlot {
        string slot_id = 1; //广告位ID，需在广告自助平台新建代码位即可获得
        int32 width = 2; //广告位宽度，以像素为单位，与屏幕密度无关 （返回图片尺寸可能会与请求有一定差异)
        int32 height = 3; //广告位高度，以像素为单位，与屏幕密度无关 （返回图片尺寸可能会与请求有一定差异）
        int32 min_duration = 4; //视频广告最小播放时长，单位为秒
        int32 max_duration = 5; //视频广告最大播放时长，单位为秒
        int32 bid_floor = 6; //广告位参竞底价，单位：分/CPM 谨慎设置，过高的底价会损失较多广告返回导致收入降低
        int32 support_deeplink = 7; //是否支持deep_link。0:不支持 1:支持(默认)
        int32 support_quick_app = 8; //是否请求快应用广告。可能返回快应用广告，回包字段quick_app_link。 1:请求快应用广告。 0:不请求快应用广告(默认)。
        int32 support_https = 9; //是否支持HTTPS并且需要HTTPS资源。 1:支持HTTPS并且需要HTTPS资源。
        int32 support_app_store = 10; //是否请求厂商应用商店下载类广告。回包字段见market_url。 1：请求厂商应用商店下载类广告。
        int32 support_mini_program = 11; //是否支持小程序调起 0:不支持(默认) 1:支持支持小程序调起
        int32 support_universal = 12; //iOS 流量使用，只在support_universal传1时有效，iOS请求如需返回universal_link，传1
        int32 support_origin_ad = 13; //是否是原生广告 1：是
        int32 pos = 14; //广告展示位置 1 顶部 2 底部 3 信息流内 4 中部 5 全屏。 穿山甲必传
    }

    message App {
        string app_id=1;//应用ID，需在广告自助平台新建应用即可获得
        string app_name=2;//APP名称，与在广告自助平台注册的应用名称保持一致
        string package_name=3;//APP包名，来源于manifest的package，与在广告自助平台注册的包名保持一致。如，com.xxx.xxx
        string version_name=4;//APP版本号，来源于manifest的versionName，而不是versionCode。如 3.5.6
        int32 version_code=5;//APP整型版本号，来源于versionCode
        string appstore_app_id=6;//在appstore的appid,iOS14以上必填
    }
    message Device {

        //设备类型
        enum DeviceType {
                USELESS_DEVICE_TYPE = 0;  //无效的,暂时不用
                PHONE = 1; // 手机(含iTouch)
                IPAD = 2; // 平板
                TV = 3; // 智能电视
                OUTDOOR_SCREEN = 4; // 户外屏幕
         };
         //屏幕方向
         enum Orientation {
                 UNKNOWN_ORIENTATION = 0;  //未知
                 VERTICAL_SCREEN = 1;  // 竖屏
                 LANDSCAPE_SCREEN = 2;  // 横屏
         };
         //广告标识授权情况，是否允许获取 IDFA
         enum SysIdfaPolicy {
                 UNKNOWN_SYSIDFAPOLICY = 0;  //未确定
                 RESTRICTED = 1;  // 受限制
                 REJECTED = 2;  // 被拒绝
                 AUTHORIZE = 3;  // 授权
         };
         //设备当前充电状态
         enum BatteryState {
                 USELESS_BATTERY_STATE = 0;  //无效的,暂时不用
                 UNKNOWN_BATTERYSTATE = 1;  //未知状态
                 CHARGING = 2;  // 正在充电
                 ELECTRO_DISCHARGE = 3;  // 放电
                 NOT_FULL = 4;  // 未充满
                 FULL = 5;  // 满状态
         };

        string idfa=1;
        string idfa_md5=2;
        repeated Caid caid=3;
        string idfv=4;
        string imei=5;
        string imei_md5=6;
        string oaid=7;
        string oaid_md5=8;
        string vaid=9;
        string android_id=10;
        string android_id_md5=11;
        string mac=12;
        string mac_md5=13;
        string imsi=14;//国际移动用户识别码
        string openudid=15;//ios设备的openUdid值
        DeviceType device_type=16;//设备类型 1:手机(含iTouch) 2:平板 3:智能电视 4:户外屏幕
        string os_type=17;//系统类型（大写）：ANDROID、IOS
        string os_version=18;//系统版本号,格式:a.b.c,如:4.3.3
        int32 api_level=19;//安卓API等级(IOS不填)
        string manufacturer=20;//1.android设备：可调用系统接口android.os.Build.MANUFACTURER直接获得 ios设备：无需填写
        string brand=21;//品牌
        string model=22;//设备型号
        int32 screen_width=23;//屏幕宽
        int32 screen_height=24;//屏幕高
        float density=25;//设备屏幕密度,如：2.0
        int32 ppi=26;//屏幕ppi
        Orientation orientation=27;//屏幕方向 0:未知 1:竖屏 2:横屏
        string hms_core=28;//HMS Core 版本号，实现被推广应用的静默安装依赖 HMS Core能力。华为设备必填
        string rom_version=29;//系统ROM版本号，华为、vivo、oppo等厂商设备必填
        string appstore_version=30;//应用商店版本号，华为、vivo、oppo设备必填，详见附录
        string browser_version=31;//浏览器版本，vivo设备必填，详见- vivo应用版本号获取方式
        string device_start_sec=32;//设备启动时间，单位：秒（保留整数） 示例："1600607106"
        string device_name=33;//设备的名称(*的 iPhone); 如：张的iPhone
        string device_name_md5=34;//设备名称的MD5值，取小写16进制的结果，长度为32个字节示例："e910dddb2748c36b47fcde5dd720eec1"
        string hardware_machine=35;//设备machine值，取值对齐model字段 示例："iPhone10,3"
        string physical_memory_byte=36;//物理内存，单位：字节 示例："4047224832"
        string harddisk_size_byte=37;//硬盘大小，单位：字节 示例："127938088960"
        string system_update_sec=38;//系统更新时间，单位：秒（保留6位小数） 示例："1595214620.383940"
        string hardware_model=39;//设备model值,示例："D22AP"
        string country=40;//国家,示例："CN"
        string language=41;//语言,示例："zh-Hans-CN"
        string time_zone=42;//时区
        int64 elapse_time=43;//开机使用时长ms
        string hardware_version=44;//硬件型号版本，例如 iPhone5S 中的 5S
        string boot_mark=45;//系统启动标识 iOS启动时间单位秒例⼦（保留整数）: 1623815045 Android例⼦: ec7f4f33-411a-47bc-8067-744a4e7e0723
        string update_mark=46;//系统更新标识 iOS更新时间单位秒例⼦（保留6位小数）:1661141691.570419 Android例⼦:1004697.709999999
        string wifiname=47;//wifi名称
        string sd_free_space=48;//磁盘剩余空间，单位：字节
        string wx_api_ver=49;//微信内部SDK版本
        int32 wx_installed=50;//仅限iOS设备,是否已安装微信, 1：是 0：否
        string opensdk_ver=51;//Android端为int，iOS端为string 微信open SDK版本
        int32 sys_cpu_num=52;//设备cpu核数;
        SysIdfaPolicy sys_idfa_policy=53;//广告标识授权情况，是否允许获取 IDFA 0:未确定 1:受限制 2:被拒绝 3:授权
        BatteryState battery_state=54;//设备当前充电状态; 1:未知状态，2:正在充电，3放电 4:未充满，5:满状态
        int32 battery=55;//设备电量; 60 代表 60%，80代表80%
        string birthtime=56;//设备初始化时间
        string paid=57;//拼多多广告识别id
        int64 sys_compling_time=58;//系统编译时间，单位毫秒
        string ssid=59;//无线网SSID名称
        string dsid=60;//苹果账号(dsid)
        string power_on_time=61;//打开手机到请求广告的时间长度
        string local_tz_time=62;//Local时区与格林威治的时间差。单位为s,如”Asia/Shanghai”=>28800。仅ios需要回传
        repeated Caid history_caid=63;//历史版本caid
        repeated string skan_version=64;//媒体支持的skan版本，ios14以上必填


        message Caid {
            string version=1;//算法版本
            string id=2;//caid
            int64 generate_time=3;//id生成时间
            VendorType vendor=4;//id 供应商  1 热云，2 信通院，3 阿里因子AAID.

            enum VendorType {
                             UN = 0;  //无用
                             RY = 1;  //热云
                             XTY = 2;  //信通院
                             AL = 3;  // 阿里因子AAID
                     };
        }
    }
    message Network {

        string ip=1;//客户端IPv4地址，注意：ip为外网 ip
        string ip_v6=2;//客户端IPv6地址
        string connection_type=3;//网络连接类型,UNKNOWN(未知)、CELL_UNKNOWN(蜂窝数据)、2G、3G、4G、5G、WIFI、ETHERNET(以太网)
        string operator_type=4;//运营商类型,CMCC(中国移动) CUCC(中国联通) CTCC(中国电信) OTHER(OTHER)
        string mcc=5;//移动国家码,如:460
        string mnc=6;// 移动网络吗,如:00
        int32 lac=7;// 位置区域码。 取值范围：0~65535
        string cellular_id=8;// 基站ID
        repeated WiFiAp wifi_aps=9;// WIFI AP对象列表，周边WIFI 热点列表，用于精细用户定位。见WiFiA对象。

        message WiFiAp {
            string ap_mac=1;// WiFiAp信息，热点 mac 地址
            bool is_connected=2;// WiFiAp信息，是否当前连接热点
            int32 rssi=3;// WiFiAp信息，热点信号强度
            string ap_name=4;// WiFiAp信息，热点名称
        }
    }
    message Geo {

        //坐标类型
         enum CoordinateType {
                 USELESS_COORDINATE_TYPE = 0;  //无效的,暂时不用
                 WGS84 = 1;  //全球卫星定位系统坐标系
                 GCJ02 = 2;  // 国家测绘局坐标系
                 BD09 = 3;  // 百度坐标系
         };

        CoordinateType coordinate_type=1;// 坐标类型。 1= WGS84(全球卫星定位系统坐标系)； 2= GCJ02（国家测绘局坐标系）； 3= BD09（百度坐标系）
        float lng=2;// 经度
        float lat=3;// 纬度
        float location_accur=4;// 经纬度精度半径，单位为米
        int64 coord_time=5;// GPS 时间戳信息 单位:毫秒
    }
    message User {
       repeated string app_list=1;// 应用安装列表
    }
}


message Response {
    int32 code = 1;//响应码
    string msg = 2;//响应说明
    string request_id = 3;//请求id
    repeated Ad ads=4;// 广告数组

    message Ad {

       string title=1;// 广告标题
       string desc=2;// 广告描述
       string icon=3;// 广告图标地址
       int32 creative_type=4;// 创意类型 1: 单图 2: 多图 3：视频
       repeated Image images=5;// 图片信息
       Video video=6;// 视频信息
       string html_snippet=7;// HTML 片段
       int32 bid_mode=8;// 出价模式： 缺省或 0-托管模式 1-实时出价模式 (一价结算) 2-实时出价模式 (二价结算) 3-PD 订单模式（以订单中约定的价格结算）
       int32 price=9;// bid_mode 非0才会填充，单位：分/CPM
       int32 interact_type=10;//可能取值： 1 - 打开网页 2 - app下载 当广告被点击后，发生的交互行为类型。对不同交互类型的广告，处理方式有差异： 1. 可以根据该参数值进行不同方式的渲染 2. 该参数取值不同，点击上报返回数据的处理方式不同，详见点击上报响应数据部分 3.对app下载类型的广告还需要进行转化上报，详见转化上报部分
       int32 prior_type=11;//配合interact_type使用，跳转优先级 0：默认 1：优先小程序跳转
       int32 pop_window=12;//是否展示确认下载弹窗，如果返回 1，需要根据应用信息 展示确认下载弹窗， 详见-弹出确认下载弹窗
       string landing_page_url=13;//落地页url
       string deeplink_url=14;//deeplink 唤醒地址
       string market_url=15;//厂商应用商店下载页url，仅Android端返回
       string package_url=16;//应用下载url，仅Android端返回
       string quick_app_link=17;//快应用url，调用后跳转快应用；请求对象中 support_quick_app 字段等于1时，可能会返回。
       string universal_link=18;//universalLink地址,IOS可能返回
       int64 app_id=19;//ios应用id，需调用iOS系统接口拉起app store的应用下载页面；iOS端可能会返回app_id
       string wechat_app_username=20;//小程序原始id
       string wechat_app_path=21;//拉起小程序页面
       string package_name=22;//下载类广告(包括deeplink) 。iOS 填写 bundleID， Android 填写包名
       int32 relation_target=23;//广告的应用安装定向类型： 0 – 不限 1 – 定向未安装 2 – 定向已安装 详见-应用安装上报与过滤策略应用
       string app_name=24;//应用名称，详见-广告五要素
       string app_icon=25;//应用图标
       int64 app_size=26;//应用文件大小，单位 KB
       string app_developer=27;//应用开发者名称
       repeated AppAuthority app_permission=28;//应用权限信息，详见-广告五要素，与permissions_url不同时存在
       string app_permissions_url=29;//应用权限列表，与app_permission不同时存在
       string app_privacy_policy=30;//应用隐私协议地址
       string app_version_name=31;//应用版本号
       repeated Tracking trackings=32;//广告效果监测列表，详见 Tracking 对象
       int32 encry_type=33;//竞价价格加密方式，竞价模式时价格宏__WIN_PRICE__需要用不同的加密方式对进行加密，具体加密方式和密钥请联系商务获取. 0：不加密(默认)
       int32 open_type=34;//落地页打开类型 0; //内开、外开由媒体决定 1; //内开，由媒体WebView打开 2; //外开，由浏览器打开
       string app_desc_url=35;//应用产品介绍
       string app_number=36;//应用备案号

        message AppAuthority {
            string permissionType = 1; //0 代表隐私权限，1 代表常规权限
            string describe = 2;//描述
            string title = 3; //权限名
        }

       message Image {
            string url=1;//图片的URL
            int32 width=2;//图片宽度，单位像素
            int32 height=3;//图片高度，单位像素
       }

       message Video {
           string url=1;//视频的URL
           int32 video_type=2;//视频类型：1=原生视频,2=激励视频
           int32 width=3;//视频宽度，单位像素
           int32 height=4;//视频高度，单位像素
           int64 size=5;//视频文件大小，单位为KB
           int32 duration=6;//视频时长（秒）
           string cover_image=7;//视频预览图 url
           int32 force_duration=8;//视频强制观看时长。单位：毫秒
       }

       message Tracking {
           string event=1;//跟踪事件类型
           repeated string urls=2;//监测链接集合，有多个监控地址时，每一个都要调用
           int32 point=3;//视频播放秒数,当事件类型是video_progress时存在 例: 3则表示需要在视频播放3秒的时候上报对应的url
       }
    }

}