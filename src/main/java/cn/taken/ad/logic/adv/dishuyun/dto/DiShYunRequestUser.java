package cn.taken.ad.logic.adv.dishuyun.dto;

import java.io.Serializable;
import java.util.List;

public class DiShYunRequestUser implements Serializable {

    private static final long serialVersionUID = 4094579310953597560L;
    /**
     * 用户在媒体上注册 ID，如果没有，传空即可
     */
    private String uid;
    /**
     * 性别：未知-0、男-1、女-2
     */
    private Integer gender;
    private Integer age;
    /**
     * 关键词列表，以逗号隔开
     */
    private String keywords;
    /**
     * 已安装的应用包名列表
     * 示例：["com.net.dhx","com.xiaomi.browser"]
     * 在《包名编号字典》内的传 instAids，未在列表内的传 appList
     */
    private List<String> appList;
    /**
     * 已安装应用的包名编号列表
     * 示例: [100, 200, 300, 400, 500, 600]
     * 在《包名编号字典》内的传 instAids，未在列表内的传 appList
     */
    private List<Integer> instAids;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public List<String> getAppList() {
        return appList;
    }

    public void setAppList(List<String> appList) {
        this.appList = appList;
    }

    public List<Integer> getInstAids() {
        return instAids;
    }

    public void setInstAids(List<Integer> instAids) {
        this.instAids = instAids;
    }
}
