package cn.taken.ad.logic.adv.huanrui;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.compress.GzipUtils;
import cn.taken.ad.component.utils.encryption.Aes;
import cn.taken.ad.component.utils.encryption.Base64;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.huanrui.dto.*;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.*;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ParamParser;
import cn.taken.ad.utils.time.TimeUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 环睿
 */
@Component("HUANRUI" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class HuanRuiAdvProcessor implements AdvProcessor {

    private static final Logger log = LoggerFactory.getLogger(HuanRuiAdvProcessor.class);

    public static final String API_VERSION = "apiVersion";
    public static final String PRICE_KEY = "priceKey";

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        Map<String, String> param = ParamParser.parseParamByJson(advDto.getPnyParam());
        String apiVersion = param.get(API_VERSION);
        HuanRuiAdvRequest request = convertRequest(rtbDto, advDto, apiVersion);
        advDto.setReqObj(request);
        String json = JsonHelper.toJsonStringWithoutNull(request);
        int z = CompareType.compareVersions(apiVersion, "2.5.5");
        HttpResult httpResult = null;
        if (z > 0) {
            byte[] bytes = GzipUtils.compress(json.getBytes(StandardCharsets.UTF_8));
            httpResult = httpClient.postBytes(advDto.getRtburl(), bytes, new Header[]{
                            new BasicHeader("Content-Type", "application/json"),
                            new BasicHeader("Accept-Encoding", "gzip"),
                            new BasicHeader("Content-Encoding", "gzip")
                    },
                    advDto.getTimeout());
        } else {
            httpResult = httpClient.postJson(advDto.getRtburl(), json, "UTF-8", new Header[]{new BasicHeader("Content-Type", "application/json"), new BasicHeader("Accept-Encoding", "gzip"), new BasicHeader("Accept-Gzip", "true")}, advDto.getTimeout());
        }
        RtbResponseDto fail = isHttpRequestFail(httpResult);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        String resp = httpResult.getDataStringUTF8();
        return parseResponse(rtbDto, advDto, resp);
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam reqDto) {
        // 是否有请求成功的
        boolean hasRight = false;

        List<String> urls = reqDto.getUrls();
        if (reqDto.getBiddingSuccess()) {
            if (null != reqDto.getPrice()) {
                String priceKey = ParamParser.parseParamByJson(reqDto.getAdvCustomParam()).get(PRICE_KEY);
                String price;
                try {
                    price = enPrice(reqDto.getPrice(), priceKey);
                } catch (Exception e) {
                    log.error("Aes Price Error,RtbId:{},AdvAccountId:{},Price:{},PriceKey:{}", reqDto.getRtbId(), reqDto.getAdvertiserId(), reqDto.getPrice(), priceKey);
                    return SuperResult.badResult("no right,price ase fail,price:" + reqDto.getPrice());
                }
                urls = replaceMacro("__PRICE__", urls, price);
            }
            if (null != reqDto.getLossPrice()) {
                String lossPrice = reqDto.getLossPrice().toString();
                urls = replaceMacro("__LOSS_PR__", urls, lossPrice);
            }
        } else {
            if (null != reqDto.getPrice()) {
                urls = replaceMacro("__AD_ECPM__", urls, reqDto.getPrice().toString());
            }
            if (StringUtils.isNotEmpty(reqDto.getAdnType())) {
                urls = replaceMacro("__ADN_TYPE__", urls, reqDto.getAdnType());
            }
            if (StringUtils.isNotEmpty(reqDto.getAdnName())) {
                urls = replaceMacro("__ADN_NAME__", urls, reqDto.getAdnName());
            }
            if (StringUtils.isNotEmpty(reqDto.getAdn())) {
                urls = replaceMacro("__AD_N__", urls, reqDto.getAdn());
            }
            if (StringUtils.isNotEmpty(reqDto.getAdTi())) {
                urls = replaceMacro("__AD_TI__", urls, reqDto.getAdTi());
            }
            if (StringUtils.isNotEmpty(reqDto.getiSS())) {
                urls = replaceMacro("__IS_S__", urls, reqDto.getiSS());
            }
            if (StringUtils.isNotEmpty(reqDto.getiSC())) {
                urls = replaceMacro("__IS_C__", urls, reqDto.getiSC());
            }
        }
        for (String url : urls) {
            HttpResult result = httpClient.get(url, null, null, -1);
            if (!hasRight) {
                hasRight = result.isSuccess();
            }
            //log.info("Req Adv Bill ReqId:{}, Result:{},HttpCode:{},Header:{},ErrMsg:{},url:{}", reqDto.getRtbId(), result.isSuccess(), result.getStatusLine(), Arrays.toString(result.getHeaders()), null != result.getThrowable() ? result.getThrowable().getMessage() : "", url);
        }
        return hasRight ? SuperResult.rightResult() : SuperResult.badResult("no right");
    }

    public RtbResponseDto parseResponse(RtbRequestDto rtbDto, RtbAdvDto advDto, String resp) throws Exception {
        HuanRuiAdvResponse response;
        try {
            advDto.setRespObj(resp);
            response = JsonHelper.fromJson(HuanRuiAdvResponse.class, resp);
        } catch (Exception e) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), "Resp convert fail");
        }
        if (!response.getMsg().equalsIgnoreCase("SUCCESS")) {
            if (response.getMsg().equals("无填充")) {
                return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
            } else {
                return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(), response.getMsg());
            }
        }
        if (null == response.getAds() || response.getAds().isEmpty()) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto();
        responseDto.setCode(LogicState.SUCCESS.getCode());
        responseDto.setAdvErrCode(response.getMsg());
        String priceKey = ParamParser.parseParamByJson(advDto.getPnyParam()).get(PRICE_KEY);
        response.getAds().forEach(tag -> {
            TagResponseDto tagResponseDto = new TagResponseDto();
            tagResponseDto.setTitle(tag.getTitle());
            tagResponseDto.setDesc(tag.getText());
            tagResponseDto.setLogoUrl(tag.getLogo());
            tagResponseDto.setIconUrl(tag.getIcon());
            if (null != tag.getImgs() && !tag.getImgs().isEmpty()) {
                tagResponseDto.setImgUrls(tag.getImgs());
            }
            if (null != tag.getBidPrice()) {
                tagResponseDto.setPrice(tag.getBidPrice().doubleValue());
            }
            tagResponseDto.setClickUrl(tag.getClkurl());
            tagResponseDto.setUniversalLink(tag.getUlkurl());
            if (StringUtils.isNotEmpty(tag.getDwnurl())) {
                tagResponseDto.setClickUrl(tag.getDwnurl());
            }

            tagResponseDto.setMarketUrl(tag.getMarketurl());
            tagResponseDto.setDeepLinkUrl(tag.getDpurl());
            tagResponseDto.setActionType(ActionType.SYSTEM_BROWSER_H5);
            if (tag.getAdct() == 1) {
                tagResponseDto.setActionType(ActionType.DOWNLOAD);
                ResponseAppDto appDto = new ResponseAppDto();
                appDto.setAppName(tag.getApp_name());
                appDto.setPackageName(tag.getPkg_name());
                appDto.setAppDeveloper(tag.getDeveloper());
                appDto.setAppVersion(tag.getApp_version());
                appDto.setAppPermissionInfoUrl(tag.getPermission_url());
                appDto.setAppPrivacyUrl(tag.getPrivacy_agreement());
                appDto.setAppInfo(tag.getIntro());
                tagResponseDto.setAppInfo(appDto);
            }
            // 监控
            List<ResponseTrackDto> tracks = new ArrayList<>();
            tagResponseDto.setTracks(tracks);
            List<HuanRuiAdvTrack> monitors = tag.getMonitors();
            if (null != monitors && !monitors.isEmpty()) {
                monitors.forEach(respTrack -> {
                    ResponseTrackDto dto = convertTrack(respTrack);
                    if (null != dto) {
                        tracks.add(dto);
                    }
                });
            }
            if (null != tag.getAdmt()) {
                if (tag.getAdmt() == 1) {
                    tagResponseDto.setMaterialType(MaterialType.VIDEO);
                    HuanRuiAdvResponseVideo hVideo = tag.getVideo();
                    if (null != hVideo) {
                        ResponseVideoDto videoDto = new ResponseVideoDto();
                        if (null != hVideo.getLength()) {
                            videoDto.setDuration(hVideo.getLength().intValue());
                        }
                        videoDto.setVideoUrl(hVideo.getUrl());
                        if (null != hVideo.getCover_img() && !hVideo.getCover_img().isEmpty()) {
                            videoDto.setCoverImgUrls(hVideo.getCover_img());
                        }
                        videoDto.setButtonText(hVideo.getButton());
                        if (null != hVideo.getEnd_img() && !hVideo.getEnd_img().isEmpty()) {
                            videoDto.setEndImgUrls(hVideo.getEnd_img());
                        }
                        videoDto.setEndHtml(hVideo.getEnd_html());
                        videoDto.setSkipSeconds(hVideo.getSkip());
                        videoDto.setEndButtonText(hVideo.getEnd_button());
                        videoDto.setEndIconUrl(hVideo.getEnd_icon());
                        videoDto.setEndTitle(hVideo.getEnd_title());
                        videoDto.setEndDesc(hVideo.getEnd_text());
                        // 暂无 vastVer  vast Vast协议版本
                        tagResponseDto.setVideoInfo(videoDto);
                    }
                } else if (tag.getAdmt() == 2) {
                    tagResponseDto.setMaterialType(MaterialType.IMAGE_TEXT);
                }
            }
            if (StringUtils.isNotEmpty(tag.getWx_id()) && StringUtils.isNotEmpty(tag.getWx_path())) {
                tagResponseDto.setActionType(ActionType.MINI_PROGRAM);
                ResponseMiniProgramDto miniProgram = new ResponseMiniProgramDto(tag.getWx_id(), tag.getWx_path());
                tagResponseDto.setMiniProgram(miniProgram);
            }
            // 暂无 replaced_ua 自定义UA，该参数有值时，需替换转换上报header中的UA
            tagResponseDto.setHtmlContent(tag.getHtmlBody());
            tagResponseDto.setClickAreaReportUrls(tag.getClick_area_report_url());
            if (null != tag.getKs_win_urls() && !tag.getKs_win_urls().isEmpty()) {
                tagResponseDto.setWinNoticeUrls(tag.getKs_win_urls());
            }
            if (null != tag.getKs_los_urls() && !tag.getKs_los_urls().isEmpty()) {
                tagResponseDto.setFailNoticeUrls(tag.getKs_los_urls());
            }
            // 宏替换
            RequestDeviceDto deviceDto = rtbDto.getDevice();
            RequestNetworkDto networkDto = rtbDto.getNetwork();
            String price;
            try {
                price = enPrice(tagResponseDto.getPrice(), priceKey);
            } catch (Exception e) {
                // 异常时 不填充
                return;
            }
            tagResponseDto.getTracks().forEach(track -> {
                List<String> urls = replaceAllMacro(track.getTrackUrls(), deviceDto, networkDto, price);
                track.setTrackUrls(urls);
            });
            responseDto.getTags().add(tagResponseDto);
        });
        return responseDto;
    }

    private String enPrice(Double price, String password) throws UnsupportedEncodingException {
        if (null == price) {
            return "";
        }
        return URLEncoder.encode(Base64.encode(Aes.encrypt(price + "", password)), "UTF-8");
    }


    private List<String> replaceAllMacro(List<String> urls, RequestDeviceDto deviceDto, RequestNetworkDto networkDto, String price) {
        urls = replaceMacro("__down_x__", urls, MacroType.DOWN_X.getCode());
        urls = replaceMacro("__down_y__", urls, MacroType.DOWN_Y.getCode());
        urls = replaceMacro("__up_x__", urls, MacroType.UP_X.getCode());
        urls = replaceMacro("__up_y__", urls, MacroType.UP_Y.getCode());
        // 暂无 相对坐标 __r_down_x__ __r_down_y__ __r_up_x__ __r_up_y__
        urls = replaceMacro("__ad_width__", urls, MacroType.WIDTH.getCode());
        urls = replaceMacro("__ad_height__", urls, MacroType.HEIGHT.getCode());
        urls = replaceMacro("__event_time_start__", urls, MacroType.START_TIME.getCode());
        urls = replaceMacro("__event_time_second__", urls, MacroType.START_TIME_SECONDS.getCode());
        urls = replaceMacro("__event_time_end__", urls, MacroType.END_TIME.getCode());
        urls = replaceMacro("__click_id__", urls, MacroType.CLICK_ID.getCode());
        // 暂无 __video_duration__ 点击视频时的播放时长，单位秒

        urls = replaceMacro("__down_x_abs__", urls, MacroType.ABS_DOWN_X.getCode());
        urls = replaceMacro("__down_y_abs__", urls, MacroType.ABS_DOWN_Y.getCode());
        urls = replaceMacro("__up_x_abs__", urls, MacroType.ABS_UP_X.getCode());
        urls = replaceMacro("__up_y_abs__", urls, MacroType.ABS_UP_Y.getCode());

        urls = replaceMacro("__gps_lon__", urls, MacroType.LON.getCode());
        urls = replaceMacro("__gps_lat__", urls, MacroType.LAT.getCode());
        // 一致 __DP_WIDTH__ __DP_HEIGHT__ __DP_DOWN_X__ __DP_DOWN_Y__ __DP_UP_X__ __DP_UP_Y__ __SLD__ __X_MAX_ACC__ __Y_MAX_ACC__ __Z_MAX_ACC__等等
        // 暂无 __ELEMENT__ 点击元素信息
        // 暂无 __SHAKE_X_MAX_ACC__  __SHAKE_Y_MAX_ACC__ __SHAKE_Z_MAX_ACC__

        String mac = networkDto.getMac();
        if (StringUtils.isNotEmpty(mac)) {
            urls = replaceMacro("__MAC__", urls, mac.replace(":", "").replace("-", ""));
            urls = replaceMacro("__MAC1__", urls, mac.replace("-", ":"));
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidId())) {
            urls = replaceMacro("__ADID__", urls, deviceDto.getAndroidId());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImei())) {
            urls = replaceMacro("__IMEI__", urls, deviceDto.getImei());
        }
        if (StringUtils.isNotEmpty(deviceDto.getOaid())) {
            urls = replaceMacro("__OAID__", urls, deviceDto.getOaid());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfa())) {
            urls = replaceMacro("__IDFA__", urls, deviceDto.getIdfa());
        }
        if (StringUtils.isNotEmpty(deviceDto.getIdfaMd5())) {
            urls = replaceMacro("__IDFA_MD5__", urls, deviceDto.getIdfaMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getImeiMd5())) {
            urls = replaceMacro("__IMEI_MD5__", urls, deviceDto.getImeiMd5());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAndroidIdMd5())) {
            urls = replaceMacro("__ADID_MD5__", urls, deviceDto.getAndroidIdMd5());
        }
        if (StringUtils.isNotEmpty(networkDto.getIp())) {
            urls = replaceMacro("__IP__", urls, MacroType.IP.getCode());
        }
        if (StringUtils.isNotEmpty(deviceDto.getUserAgent())) {
            urls = replaceMacro("__UA__", urls, MacroType.UA.getCode());
        }
        if (StringUtils.isNotEmpty(deviceDto.getAaid())) {
            urls = replaceMacro("__ALL_AAID__", urls, deviceDto.getAaid());
        }

        String caid = "";
        if (deviceDto.getCaids() != null && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto deviceDtoCaid : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(deviceDtoCaid.getCaid())) {
                    caid = deviceDtoCaid.getCaid();
                    break;
                }
            }
        }
        if (StringUtils.isNotEmpty(caid)) {
            urls = replaceMacro("__CAID__", urls, caid);
        }
        if (StringUtils.isNotEmpty(price)) {
            urls = replaceMacro("__PRICE__", urls, price);
        }
        // 暂无 __CLICK_MODE__ 点击动作类型 0:未知1:正常点击，即常规触屏点击2:摇一摇3:划动点击
        // 暂无 __SHAKE_X_MAX_ACC__
        // 暂无 __SHAKE_Y_MAX_ACC__
        // 暂无 __SHAKE_Z_MAX_ACC__ 设备摇动时，z 轴加速度 单位:m/s2  用户摇动点击时 z 轴加速度峰值 100 倍取整;仅广 告交互方式=2(摇一摇)时替换
        // __TARGET_APP_INSTALL__ 与平台一致
        return urls;
    }

    private ResponseTrackDto convertTrack(HuanRuiAdvTrack track) {
        EventType eventType = convertEventType(track.getEvent());
        if (null == eventType) {
            return null;
        }
        if (null == track.getUrls() || track.getUrls().isEmpty()) {
            return null;
        }
        ResponseTrackDto dto = new ResponseTrackDto();
        dto.setTrackUrls(track.getUrls());
        dto.setTrackType(eventType.getType());
        return dto;
    }

    private EventType convertEventType(Integer type) {
        if (null == type) {
            return null;
        }
        switch (type) {
            case 0:
                return EventType.EXPOSURE;
            case 1:
                return EventType.CLICK;
            case 2:
                return EventType.DOWNLOAD_BEGIN;
            case 3:
                return EventType.DOWNLOAD_COMPLETED;
            case 4:
                return EventType.INSTALL_BEGIN;
            case 5:
                return EventType.INSTALL_COMPLETED;
            case 6:
                return EventType.INSTALLED_OPEN;
            case 8:
                return EventType.CLOSE_AD;
            case 9:
                return EventType.DEEPLINK_OPEN_FAIL;
            case 91:
                return EventType.DEEPLINK_START;
            case 10:
                return EventType.DEEPLINK_OPEN_SUCCESS;
            case 11:
                return EventType.VIDEO_BEGIN;
            case 12:
                return EventType.VIDEO_25;
            case 13:
                return EventType.VIDEO_50;
            case 14:
                return EventType.VIDEO_75;
            case 15:
                return EventType.VIDEO_END;
            case 16:
                return EventType.VIDEO_SKIP;
            case 17:
                return EventType.VIDEO_FULL_SCREEN;
            case 18:
                return EventType.VIDEO_EXITS_FULL_SCREEN;
            case 19:
                return EventType.VIDEO_LOADED_SUCCESS;
            case 20:
                return EventType.VIDEO_LOADED_FAIL;
            case 21:
                return EventType.VIDEO_MUTE;
            case 22:
                return EventType.VIDEO_UNMUTE;
            case 23:
                return EventType.VIDEO_PAUSE;
            case 24:
                return EventType.VIDEO_CONTINUE;
            case 25:
                return EventType.VIDEO_PLAY_ERROR;
            case 26:
                return EventType.VIDEO_PLAY_REPLAY;
            case 27:
                return EventType.VIDEO_SLIDE_UP;
            case 28:
                return EventType.VIDEO_SLIDE_DOWN;
            case 29:
                return EventType.VIDEO_PLAY_EDN_DISPLAYED;

        }
        return null;
    }


    /**
     * 参数转换为广告主需要的格式
     */
    private HuanRuiAdvRequest convertRequest(RtbRequestDto rtbDto, RtbAdvDto advDto, String apiVersion) {

        HuanRuiAdvRequest request = new HuanRuiAdvRequest();
        request.setTag(rtbDto.getReqId());
        request.setVer(apiVersion);
        // app
        request.setApp(convertRequestApp(rtbDto, advDto));
        // device
        request.setDevice(convertRequestDevice(rtbDto));
        request.setGps(convertRequestGps(rtbDto));
        // network
        request.setNet(convertRequestNetWork(rtbDto));
        //user
        request.setUser(convertRequestUser(rtbDto));
        return request;
    }

    private HuanRuiAdvRequestDevice convertRequestDevice(RtbRequestDto rtbDto) {
        HuanRuiAdvRequestDevice request = new HuanRuiAdvRequestDevice();
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        RequestAppDto appDto = rtbDto.getApp();
        RequestTagDto tagDto = rtbDto.getTag();
        RequestGeoDto geoDto = rtbDto.getGeo();

        OsType osType = deviceDto.getOsType();
        if (null == osType) {
            request.setOs(2);
        } else {
            switch (osType) {
                case IOS:
                    request.setOs(1);
                    break;
                case ANDROID:
                    request.setOs(0);
                    break;
                default:
                    request.setOs(2);
                    break;
            }
        }
        request.setOsv(deviceDto.getOsVersion());
        request.setOsl(deviceDto.getApiLevel());
        request.setBrand(deviceDto.getBrand());
        request.setModel(deviceDto.getModel());
        // 暂无 dpi
        request.setPpi(deviceDto.getPpi());
        request.setDensity(deviceDto.getScreenDensity());
        request.setSw(deviceDto.getWidth());
        request.setSh(deviceDto.getHeight());
        request.setUa(deviceDto.getUserAgent());
        List<RequestInstalledAppDto> installedAppDtos = deviceDto.getInstalledAppInfo();
        if (null != installedAppDtos) {
            List<String> packageNames = new ArrayList<>();
            installedAppDtos.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getPackageName())) {
                    packageNames.add(item.getPackageName());
                }
            });
            if (!packageNames.isEmpty()) {
                request.setInstalls(packageNames);
            }
        }

        request.setOaid(deviceDto.getOaid());
        request.setImsi(deviceDto.getImsi());
        request.setImei(deviceDto.getImei());
        request.setImei_md5(deviceDto.getImeiMd5());
        request.setSerial_no(deviceDto.getSerialNO());
        request.setAndroidid(deviceDto.getAndroidId());
        request.setAndroidid_md5(deviceDto.getAndroidIdMd5());
        request.setAaid(deviceDto.getAaid());
        request.setIdfa(deviceDto.getIdfa());
        request.setIdfa_md5(deviceDto.getIdfaMd5());
        request.setIdfv(deviceDto.getIdfv());
        request.setOpenudid(deviceDto.getOpenUdId());
        request.setSsid(networkDto.getSsid());
        request.setWifi_mac(networkDto.getWifiMac());
        request.setRom_version(deviceDto.getRomVersion());
        if (StringUtils.isNotEmpty(deviceDto.getBrand()) && deviceDto.getBrand().toUpperCase().contains("XIAOMI")) {
            if (StringUtils.isNotEmpty(deviceDto.getSysUiVersion())) {
                request.setMiui_version(deviceDto.getSysUiVersion());
            }
        }
        request.setSys_compling_time(StringUtils.isNotEmpty(deviceDto.getSysCompileTime()) ? deviceDto.getSysCompileTime() : "");
        request.setBirth_time(StringUtils.isNotEmpty(deviceDto.getSysInitTime()) ? deviceDto.getSysInitTime() : "");
        request.setStore_url(appDto.getAppstoreUrl());
        request.setCan_deepLink(1);
        request.setPhone_name(deviceDto.getDeviceName());
        request.setPhone_name_md5(deviceDto.getDeviceNameMd5());
        request.setLanguage(deviceDto.getLanguage());
        request.setCountry(deviceDto.getCountry());
        request.setBoot_time(null == deviceDto.getSysStartTime() ? "" : deviceDto.getSysStartTime());
        request.setOp_up_time(null == deviceDto.getSysUpdateTime() ? "" : deviceDto.getSysUpdateTime());

        request.setSystem_boot_time_nanoSec(null == deviceDto.getSysStartTime() ? "" : deviceDto.getSysStartTime());
        request.setSystem_update_time_nanoSec(null == deviceDto.getSysUpdateTime() ? "" : deviceDto.getSysUpdateTime().replace(".", "::"));
        Long startTime = TimeUtils.convertMilliSecond(deviceDto.getSysStartTime());
        request.setSystem_boot_time_milliSec(null != startTime ? startTime.toString() : "");
        request.setElapsetime(null != deviceDto.getSysElapseTime() ? deviceDto.getSysElapseTime() : "");
        if (null != deviceDto.getDeviceHardDisk()) {
            //转GB
            int size = (int) (deviceDto.getDeviceHardDisk() / 1024 / 1024 / 1024);
            request.setDisk_size(size);
        }
        if (null != deviceDto.getDeviceMemory()) {
            //转GB
            int size = (int) (deviceDto.getDeviceMemory() / 1024 / 1024 / 1024);
            request.setMemory_size(size);
        }
        request.setBattery_status("Unkown");
        if (null != deviceDto.getBatteryStatus()) {
            switch (deviceDto.getBatteryStatus()) {
                case 2:
                    request.setBattery_status("Unplugged");
                    break;
                case 3:
                    request.setBattery_status("Charging");
                    break;
                case 4:
                    request.setBattery_status("Full");
                    break;
            }
        }
        if (null != deviceDto.getBatteryPower()) {
            request.setBattery_power(deviceDto.getBatteryPower().toString());
        }
        request.setCpu_number(deviceDto.getCpuNum());
        if (null != deviceDto.getCpuFreq()) {
            request.setCpu_frequency(deviceDto.getCpuFreq().toString());
        }
        request.setTime_zone(deviceDto.getTimeZone());
        request.setHardware_model(deviceDto.getHardwareModel());
        request.setHardware_machine(deviceDto.getHardwareMachine());
        request.setAuth_status(0);
        request.setLmt(0);
        if (null != deviceDto.getIdfaPolicy()) {
            switch (deviceDto.getIdfaPolicy()) {
                case 1:
                    request.setAuth_status(0);
                    request.setLmt(0);
                    break;
                case 2:
                    request.setAuth_status(1);
                    request.setLmt(1);
                    break;
                case 3:
                    request.setAuth_status(2);
                    request.setLmt(2);
                    break;
                case 4:
                    request.setAuth_status(3);
                    request.setLmt(3);
                    break;
            }
        }
        request.setSkadnetwork_versions(deviceDto.getSkanVersion());
        Integer laccu = null;
        if (null != geoDto.getLatitude()) {
            int places = getDecimalPlaces(geoDto.getLatitude());
            laccu = places > 3 ? 0 : 1;
        }
        if (null != laccu) {
            request.setLaccu(laccu);
        }

        List<HuanRuiAdvRequestCaid> caids = new ArrayList<>();
        if (null != deviceDto.getCaids() && !deviceDto.getCaids().isEmpty()) {
            for (RequestCaidDto caid : deviceDto.getCaids()) {
                if (StringUtils.isNotEmpty(caid.getCaid())) {
                    caids.add(new HuanRuiAdvRequestCaid(caid.getCaid(), caid.getVersion()));
                }
            }
        }
        request.setCaids(caids);
        request.setApp_store_version(deviceDto.getAppStoreVersion());
        request.setHms_ver(deviceDto.getHmsVersion());
        request.setHwag_ver(deviceDto.getHmsAgVersion());
        request.setBoot_mark(deviceDto.getBootMark());
        request.setUpdate_mark(deviceDto.getUpdateMark());
        request.setPaid(deviceDto.getPaid());
        return request;
    }

    private HuanRuiAdvRequestUser convertRequestUser(RtbRequestDto rtbDto) {
        HuanRuiAdvRequestUser request = new HuanRuiAdvRequestUser();
        RequestUserDto userDto = rtbDto.getUser();
        request.setAge(userDto.getAge());
        //request.setCityName(""); 暂无 所在城市
        if (StringUtils.isNotEmpty(userDto.getGender())) {
            if (userDto.getGender().equalsIgnoreCase("M")) {
                request.setSex("男");
            } else if (userDto.getGender().equalsIgnoreCase("F")) {
                request.setSex("女");
            }
        }
        // 暂无 pddTagId PDD预算必填
        return request;
    }

    private HuanRuiAdvRequestGps convertRequestGps(RtbRequestDto rtbDto) {
        HuanRuiAdvRequestGps request = new HuanRuiAdvRequestGps();
        RequestGeoDto geoDto = rtbDto.getGeo();
        if (null != geoDto.getLatitude()) {
            request.setLat(geoDto.getLatitude().toString());
        }
        if (null != geoDto.getLongitude()) {
            request.setLon(geoDto.getLongitude().toString());
        }
        CoordinateType coordinateType = geoDto.getCoordinateType();
        request.setCoordinateType(0);
        if (null != coordinateType) {
            switch (coordinateType) {
                case GLOBAL:
                    request.setCoordinateType(1);
                    break;
                case STATE:
                    request.setCoordinateType(2);
                    break;
                case BAIDU:
                    request.setCoordinateType(3);
                    break;
            }
        }
        return request;
    }

    private HuanRuiAdvRequestNet convertRequestNetWork(RtbRequestDto rtbDto) {
        RequestNetworkDto networkDto = rtbDto.getNetwork();
        HuanRuiAdvRequestNet request = new HuanRuiAdvRequestNet();
        String ip = networkDto.getIp();
        if (StringUtils.isEmpty(ip) && StringUtils.isNotEmpty(networkDto.getIpv6())) {
            ip = networkDto.getIpv6();
        }
        request.setIp(ip);

        ConnectionType connectionType = networkDto.getConnectType();
        if (connectionType != null) {
            switch (connectionType) {
                case WIFI:
                    request.setNetwork("wifi");
                    break;
                case NETWORK_2G:
                    request.setNetwork("2g");
                    break;
                case NETWORK_3G:
                    request.setNetwork("3g");
                    break;
                case NETWORK_4G:
                    request.setNetwork("4g");
                    break;
                case NETWORK_5G:
                    request.setNetwork("5g");
                    break;
                default:
                    request.setNetwork("other");
                    break;
            }
        }
        request.setMac(networkDto.getMac());
        CarrierType carrierType = networkDto.getCarrierType();
        if (carrierType != null) {
            switch (carrierType) {
                case CM:
                    request.setOperator("1");
                    break;
                case CU:
                    request.setOperator("2");
                    break;
                case CT:
                    request.setOperator("3");
                    break;
                default:
                    request.setOperator("0");
                    break;
            }
        }

        return request;
    }

    private HuanRuiAdvRequestApp convertRequestApp(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        HuanRuiAdvRequestApp request = new HuanRuiAdvRequestApp();
        RequestAppDto appDto = rtbDto.getApp();
        request.setAppid(advDto.getAppCode());
        request.setSlotid(advDto.getTagCode());
        request.setName(appDto.getAppName());
        request.setPkgname(appDto.getBundle());
        request.setVer(appDto.getAppVersion());
        request.setOrientation(0);
        if (null != rtbDto.getTag().getPrice()) {
            request.setPrice(rtbDto.getTag().getPrice().floatValue());
        }
        return request;
    }

    private int getDecimalPlaces(Double val) {
        if (null == val) {
            return 0;
        }
        String numberStr = Double.toString(val);
        int decimalIndex = numberStr.indexOf('.');

        if (decimalIndex == -1) {
            return 0; // 如果没有小数点，返回0
        }
        return numberStr.length() - decimalIndex - 1;
    }
}
