<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" class="ruleForm" size="mini">
        <el-form-item label="媒体" prop="mediaId">
          <el-select
            v-model="listQuery.mediaId"
            filterable
            clearable
            placeholder="请选择媒体"
            style="width: 100%"
            remote
            :remote-method="loadMedias"
          >
            <el-option v-for="media in medias" :key="media.id" :label="media.name + ' - ' + media.code" :value="media.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统" prop="type">
          <el-select v-model="listQuery.type" filterable clearable placeholder="请选择系统" style="width: 100%">
            <el-option v-for="item in osTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="listQuery.state" filterable clearable placeholder="请选择状态" style="width: 100%">
            <el-option v-for="item in auditStateList" :key="item.type" :label="item.name" :value="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="APP">
          <el-input v-model="listQuery.appName" placeholder="名称或CODE或包名" />
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="primary" @click="handleFilter">查询</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button class="filter-item" type="info" @click="remove">清除</el-button>
        </el-form-item>
        <el-form-item label>
          <el-button v-if="AuthUtils.hasAuth('MEDIA_APP_ADD')" type="success" icon="el-icon-circle-plus-outline" @click="handleAdd">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :row-class-name="tableRowClassName"
    >
      <el-table-column align="center" label="序号" width="60" type="index" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="包名" align="center" prop="packageName" />
      <el-table-column label="CODE" align="center" prop="code" />
      <el-table-column label="系统" align="center" prop="type">
        <template slot-scope="scope">
          {{ parseOsType(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="所属媒体" align="center">
        <template slot-scope="scope"> {{ scope.row.mediaName }} - {{ scope.row.mediaCode }} </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="state">
        <template slot-scope="scope">
          {{ parseAuditState(scope.row.state) }}
          <template v-if="scope.row.state === 3">
            <br />
            <span>{{ scope.row.suggest }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="AuthUtils.hasAuth('MEDIA_APP_MODIFY') && scope.row.state !== 3"
            size="mini"
            type="success"
            icon="el-icon-edit"
            circle
            title="编辑"
            @click="handleModify(scope.row)"
          />
          <el-button
            v-if="scope.row.state == 1 && AuthUtils.hasAuth('MEDIA_APP_AUDIT')"
            size="mini"
            type="warning"
            icon="el-icon-star-off"
            circle
            title="审核"
            @click="handleAudit(scope.row)"
          />
          <el-button
            v-if="AuthUtils.hasAuth('MEDIA_APP_MODIFY')"
            size="mini"
            type="warning"
            icon="el-icon-document-copy"
            circle
            title="复制"
            @click="handleCopy(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-if="paginationShow"
      class="pagination"
      :total="total"
      :total-page="totalPage"
      :page="currentPageNum"
      :start.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-drawer
      title="新增APP"
      :show-close="false"
      size="50%"
      :visible.sync="addOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaAppAdd v-if="addOpen" :is-update.sync="addUpdate" @changePageOpen="changeAddOpen" />
    </el-drawer>

    <el-drawer
      title="修改APP"
      :show-close="false"
      size="50%"
      :visible.sync="modifyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaAppModify v-if="modifyOpen" :id.sync="infoId" :is-update.sync="modifyUpdate" @changePageOpen="changeModifyOpen" />
    </el-drawer>

    <el-drawer
      title="审核APP"
      :show-close="false"
      size="50%"
      :visible.sync="auditOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <MediaAppAudit v-if="auditOpen" :id.sync="infoId" :is-update.sync="auditUpdate" @changePageOpen="changeAuditOpen" />
    </el-drawer>
    <el-drawer
      title="复制APP"
      :show-close="false"
      size="50%"
      :visible.sync="copyOpen"
      :wrapper-closable="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <AdvertiserAppAdd v-if="copyOpen" :copy-app.sync="copyApp" :is-update.sync="copyUpdate" @changePageOpen="changeCopyOpen" />
    </el-drawer>
  </div>
</template>
<script>
import { pageMediaApp } from '@/api/media/mediaApp'
import { listMedia } from '@/api/media/mediaMain'
import { listOsTypeApi } from '@/api/public/typeinfo'
import Pagination from '@/components/Pagination'
import MediaAppAdd from './MediaAppAdd' // 新增
import MediaAppModify from './MediaAppModify' // 修改
import MediaAppAudit from './MediaAppAudit' // 修改
import AdvertiserAppAdd from '../../advertiser/app/AdvertiserAppAdd' // 修改
export default {
  name: 'MediaApp',
  components: { Pagination, MediaAppAdd, MediaAppModify, MediaAppAudit, AdvertiserAppAdd },
  data() {
    return {
      tableKey: 0,
      paginationShow: true,
      list: [],
      total: 0,
      totalPage: 0,
      currentPageNum: 0,
      listLoading: false,
      listQuery: {
        mediaId: null,
        appName: null,
        type: null,
        state: null,
        start: 0,
        limit: 20
      },
      osTypeList: [],
      auditStateList: [
        { name: '待审', type: 1 },
        { name: '通过', type: 2 },
        { name: '驳回', type: 3 }
      ],
      medias: [],
      appList: [],
      appLoading: false,
      infoId: null,
      addOpen: false,
      addUpdate: false,
      modifyOpen: false,
      modifyUpdate: false,
      auditOpen: false,
      auditUpdate: false,
      copyOpen: false,
      copyUpdate: false,
      copyApp: null
    }
  },
  computed: {},
  created() {
    listOsTypeApi().then(response => {
      this.osTypeList = response.result.filter(item => item.id !== 999)
    })
    this.loadMedias(null)
    this.getList()
  },
  methods: {
    parseAuditState(type) {
      for (const item of this.auditStateList) {
        if (item.type === type) {
          return item.name
        }
      }
      return '未知'
    },
    parseOsType(id) {
      const value = this.osTypeList.filter(item => item.id === id)
      return value && value.length > 0 ? value[0].name : '未知'
    },
    loadMedias(name) {
      listMedia({ name: name }).then(response => {
        this.medias = response.result
      })
    },
    remove() {
      this.listQuery.start = 0
      this.listQuery.limit = 20
      this.listQuery.appName = null
      this.listQuery.mediaId = null
      this.listQuery.type = null
      this.listQuery.state = null
      this.getList()
    },
    handleFilter() {
      this.listQuery.start = 0
      this.getList()
    },
    createMM() {
      this.$emit('changePageOpen', false)
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return 'success-row'
      }
      return ''
    },
    handleAdd() {
      this.addOpen = true
    },
    getList() {
      this.list = []
      this.total = 0
      this.totalPage = 0
      this.currentPageNum = 0
      this.paginationShow = false
      this.listLoading = true
      pageMediaApp(this.listQuery).then(response => {
        this.list = response.result.list
        this.total = response.result.totalCount
        this.totalPage = response.result.totalPage
        this.currentPageNum = response.result.currentPageNum
        this.listLoading = false
        this.paginationShow = true
      })
    },
    handleModify(row) {
      this.infoId = row.id
      this.modifyOpen = true
    },
    changeAddOpen(open) {
      this.addOpen = open
      if (this.addUpdate) {
        this.getList()
      }
    },
    changeModifyOpen(open) {
      this.modifyOpen = open
      if (this.modifyUpdate) {
        this.getList()
      }
    },
    handleAudit(row) {
      this.infoId = row.id
      this.auditOpen = true
    },
    changeAuditOpen(open) {
      this.auditOpen = open
      if (this.auditUpdate) {
        this.getList()
      }
    },
    handleCopy(row) {
      this.copyApp = row
      this.copyOpen = true
    },
    changeCopyOpen(open) {
      this.copyOpen = open
      this.copyApp = null
    }
  }
}
</script>

<style scoped>
.el-select .el-input {
  width: 100%;
}
span {
  color: rgb(49, 47, 47);
}
::deep .el-drawer__body {
  overflow: auto;
}
</style>
