[2025-06-28 20:57:53.205] [cn.taken.ad.RtbApplication.logStarting,50] INFO  - Starting RtbApplication on chang with PID 34852 (D:\ideaProjects\ssc\ssp-rtb-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-06-28 20:57:53.219] [cn.taken.ad.RtbApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-06-28 20:57:55.024] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - Tomcat initialized with port(s): 9090 (http)
[2025-06-28 20:57:55.050] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-9090"]
[2025-06-28 20:57:55.062] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-06-28 20:57:55.063] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-06-28 20:57:55.173] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-06-28 20:57:55.173] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 1899 ms
[2025-06-28 20:57:56.084] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 20:57:57.401] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-9090"]
[2025-06-28 20:57:57.552] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 9090 (http) with context path ''
[2025-06-28 20:57:57.556] [cn.taken.ad.RtbApplication.logStarted,59] INFO  - Started RtbApplication in 4.953 seconds (JVM running for 6.277)
[2025-06-28 20:57:57.560] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-06-28 20:57:57.562] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-06-28 20:57:57.575] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-06-28 20:57:57.577] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.startLoadBalance,60] INFO  - not need change load balance
[2025-06-28 20:58:01.067] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 20:58:01.072] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 20:58:01.072] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 20:58:01.067] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 20:58:01.073] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 20:58:04.715] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-28 20:58:04.715] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-06-28 20:58:04.722] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 7 ms
[2025-06-28 20:58:04.961] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 20:58:26.722] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 20:58:33.305] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 20:59:11.386] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 20:59:11.387] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 20:59:11.388] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 20:59:13.460] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 20:59:36.606] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"36-125-16-30-63-21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282058","strategyId":36,"strategyTagAdvId":125,"mediaId":16,"mediaAppId":30,"mediaTagId":63,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":1153,"mediaAvgTime":1153,"mediaMinTime":0,"mediaUseTimeTotal":1153,"advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"advertiserReqTotal":2,"advertiserReqSuccessTotal":2,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":22347,"advertiserAvgTime":11526,"advertiserMinTime":0,"advertiserUseTimeTotal":23052}},minuteMediaReq:{"36-16-30-63":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282058","mediaId":16,"mediaAppId":30,"mediaTagId":63,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":36,"maxTime":1153,"avgTime":1153,"minTime":0,"useTimeTotal":1153}},minuteAdvReq:{"21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282058","advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"reqTotal":2,"reqSuccessTotal":2,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":22347,"avgTime":11526,"minTime":0,"useTimeTotal":23052}}
[2025-06-28 20:59:36.644] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"36-16-30-63-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282058","mediaId":16,"mediaAppId":30,"mediaTagId":63,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":36}},advertiserErrorCode:{}
[2025-06-28 20:59:43.533] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 20:59:58.340] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 21:00:01.084] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:00:01.084] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:00:01.084] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:00:01.182] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"36-125-16-30-63-21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282059","strategyId":36,"strategyTagAdvId":125,"mediaId":16,"mediaAppId":30,"mediaTagId":63,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":67325,"mediaAvgTime":67325,"mediaMinTime":0,"mediaUseTimeTotal":67325,"advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"advertiserReqTotal":0,"advertiserReqSuccessTotal":0,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":0,"advertiserAvgTime":null,"advertiserMinTime":0,"advertiserUseTimeTotal":0}},minuteMediaReq:{"36-16-30-63":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282059","mediaId":16,"mediaAppId":30,"mediaTagId":63,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":36,"maxTime":67325,"avgTime":67325,"minTime":0,"useTimeTotal":67325}},minuteAdvReq:{"21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282059","advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"reqTotal":0,"reqSuccessTotal":0,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":0,"avgTime":null,"minTime":0,"useTimeTotal":0}}
[2025-06-28 21:00:01.214] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"36-16-30-63-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282059","mediaId":16,"mediaAppId":30,"mediaTagId":63,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":36}},advertiserErrorCode:{}
[2025-06-28 21:00:13.604] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:00:26.908] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 21:00:45.806] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:01:04.609] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,66] INFO  - start upload chunk file
[2025-06-28 21:01:04.614] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:01:04.614] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:01:04.616] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:01:04.634] [cn.taken.ad.task.DmpPackageSaveTask.uploadFile,103] INFO  - finished upload chunk file
[2025-06-28 21:01:30.771] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:01:30.890] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"36-125-16-30-63-21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282100","strategyId":36,"strategyTagAdvId":125,"mediaId":16,"mediaAppId":30,"mediaTagId":63,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":14253,"mediaAvgTime":14253,"mediaMinTime":0,"mediaUseTimeTotal":14253,"advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":1726,"advertiserAvgTime":1726,"advertiserMinTime":0,"advertiserUseTimeTotal":1726}},minuteMediaReq:{"36-16-30-63":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282100","mediaId":16,"mediaAppId":30,"mediaTagId":63,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":36,"maxTime":14253,"avgTime":14253,"minTime":0,"useTimeTotal":14253}},minuteAdvReq:{"21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282100","advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":1726,"avgTime":1726,"minTime":0,"useTimeTotal":1726}}
[2025-06-28 21:01:43.580] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"36-16-30-63-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282100","mediaId":16,"mediaAppId":30,"mediaTagId":63,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":36}},advertiserErrorCode:{}
[2025-06-28 21:02:34.325] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:02:34.325] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:02:34.325] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:02:34.325] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:02:34.325] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:02:34.396] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:02:36.306] [cn.taken.ad.api.RtbApi.rtb,314] INFO  - RtbId:17511154754590380,rtb mediaCode:6f0a4c40,appCode:1309260435081859072,tagCode:1309261134989561856,code:SUCCESS_NON_PARTICIPATION,msg:无填充
[2025-06-28 21:03:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:03:01.003] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:03:01.003] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:03:01.034] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"36-16-30-63-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282102","mediaId":16,"mediaAppId":30,"mediaTagId":63,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":36}},advertiserErrorCode:{}
[2025-06-28 21:03:01.129] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{"36-125-16-30-63-21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282102","strategyId":36,"strategyTagAdvId":125,"mediaId":16,"mediaAppId":30,"mediaTagId":63,"mediaReqTotal":1,"mediaReqInvalidTotal":0,"mediaRespFailTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"mediaMaxTime":129399,"mediaAvgTime":129399,"mediaMinTime":0,"mediaUseTimeTotal":129399,"advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"advertiserReqTotal":1,"advertiserReqSuccessTotal":1,"advertiserReqFailTotal":0,"advertiserRespFailTotal":0,"advertiserReqTimeoutTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0,"advertiserMaxTime":125948,"advertiserAvgTime":125948,"advertiserMinTime":0,"advertiserUseTimeTotal":125948}},minuteMediaReq:{"36-16-30-63":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282102","mediaId":16,"mediaAppId":30,"mediaTagId":63,"reqTotal":1,"reqInvalidTotal":0,"respFailTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"strategyId":36,"maxTime":129399,"avgTime":129399,"minTime":0,"useTimeTotal":129399}},minuteAdvReq:{"21-6-53":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282102","advertiserId":21,"advertiserAppId":6,"advertiserTagId":53,"reqTotal":1,"reqSuccessTotal":1,"reqFailTotal":0,"respFailTotal":0,"reqTimeoutTotal":0,"participatingTotal":0,"winTotal":0,"amount":0,"maxTime":125948,"avgTime":125948,"minTime":0,"useTimeTotal":125948}}
[2025-06-28 21:03:04.474] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:03:34.550] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:04:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:04:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:04:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:04:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:04:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:04:04.650] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:04:34.738] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:05:01.010] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:05:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:05:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:05:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:05:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:05:04.829] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:05:34.905] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:06:01.004] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:06:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:06:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:06:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:06:01.005] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:06:04.985] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:06:35.067] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:07:01.015] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:07:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:07:01.015] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:07:01.016] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:07:01.017] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:07:05.146] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:07:35.222] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:08:01.009] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:08:01.009] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:08:01.009] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:08:01.010] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:08:01.010] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:08:05.297] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:08:35.372] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:09:01.005] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:09:01.005] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:09:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:09:01.005] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:09:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:09:05.460] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:09:35.542] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:10:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:10:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:10:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:10:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:10:01.006] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:10:05.633] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:10:35.714] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:11:01.006] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:11:01.006] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:11:01.007] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:11:01.006] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:11:01.007] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:11:05.796] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:11:35.889] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:12:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:12:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:12:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:12:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:12:01.014] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:12:05.975] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:12:36.053] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:13:01.011] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:13:01.012] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:13:01.013] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:13:01.013] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:13:01.015] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:13:06.133] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:13:36.217] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:14:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:14:01.008] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:14:01.008] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:14:01.008] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:14:01.010] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:14:06.305] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:14:36.391] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
[2025-06-28 21:15:01.014] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:15:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:15:01.014] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:15:01.014] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:15:01.015] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:15:06.476] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 0dd33761efb842fdb1b2c71d4fce030d , 80
