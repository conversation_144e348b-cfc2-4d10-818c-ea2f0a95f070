[2025-06-28 21:19:47.626] [cn.taken.ad.RtbApplication.logStarting,50] INFO  - Starting RtbApplication on chang with PID 11832 (D:\ideaProjects\ssc\ssp-rtb-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-06-28 21:19:47.635] [cn.taken.ad.RtbApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-06-28 21:19:49.324] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - Tomcat initialized with port(s): 9090 (http)
[2025-06-28 21:19:49.347] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-9090"]
[2025-06-28 21:19:49.357] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-06-28 21:19:49.359] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-06-28 21:19:49.471] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-06-28 21:19:49.471] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 1792 ms
[2025-06-28 21:19:50.701] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server RTB , 570008194f634200bcc1f774ffe0c2e2 , 81
[2025-06-28 21:19:52.107] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-9090"]
[2025-06-28 21:19:52.801] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 9090 (http) with context path ''
[2025-06-28 21:19:52.819] [cn.taken.ad.RtbApplication.logStarted,59] INFO  - Started RtbApplication in 5.672 seconds (JVM running for 6.651)
[2025-06-28 21:19:52.833] [org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler.initialize,171] INFO  - Initializing ExecutorService
[2025-06-28 21:19:52.836] [cn.taken.ad.component.superscheduler.SuperScheduler.start,102] INFO  - super-scheduler starting
[2025-06-28 21:19:52.885] [cn.taken.ad.component.superscheduler.SuperScheduler.start,126] INFO  - super-scheduler started
[2025-06-28 21:19:52.885] [cn.taken.ad.configuration.lb.HSLoadBalanceListener.startLoadBalance,60] INFO  - not need change load balance
[2025-06-28 21:19:59.801] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-28 21:19:59.802] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-06-28 21:19:59.808] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 6 ms
[2025-06-28 21:20:00.345] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 21:20:03.370] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:20:03.370] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:20:03.370] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:20:03.370] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:20:03.371] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:20:30.456] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 570008194f634200bcc1f774ffe0c2e2 , 81
[2025-06-28 21:20:43.310] [cn.taken.ad.api.RtbApi.rtb,314] INFO  - RtbId:17511167897840081,rtb mediaCode:6f0a4c40,appCode:1309260435081859072,tagCode:1309261134989561856,code:SUCCESS_NON_PARTICIPATION,msg:无填充
[2025-06-28 21:21:59.857] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:21:59.857] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:21:59.857] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:21:59.884] [cn.taken.ad.logic.media.yinghuochong.YingHuoChongMediaProcessor.parseRtb,189] INFO  - request:{"reqId":"f_b713a7d37c616d6148eb4468912","app":{"appId":"1309260435081859072","appVersion":"2.0.0","bundle":"com.xx.yy","appName":"1121应用","appVersionCode":null,"appstoreUrl":null,"appDomainUrl":null},"tag":{"tagId":"1309261134989561856","size":1,"tagType":"OTHER","width":null,"height":null,"minDuration":null,"maxDuration":1,"needHttps":null,"price":0.0,"query":null},"device":{"serialNO":null,"deviceName":null,"deviceNameMd5":null,"osType":"UNKNOWN","deviceType":"UNKNOWN","osVersion":"1234","brand":"BTKR-W00","model":"BTKR-W00","modelCode":null,"androidId":null,"androidIdMd5":null,"imei":null,"imeiMd5":null,"oaid":"43ef6aa3a218044f123","oaidMd5":"11112","idfa":null,"idfaMd5":null,"vaid":null,"vaidMd5":null,"idfaPolicy":null,"batteryStatus":null,"batteryPower":null,"openUdId":null,"idfv":null,"idfvMd5":null,"imsi":null,"imsiMd5":null,"width":200,"height":1000,"orientation":null,"userAgent":"Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36","screenDensity":null,"screenInch":null,"ppi":null,"deviceMemory":null,"deviceHardDisk":null,"timeZone":null,"language":null,"country":null,"cpuNum":null,"hardwareMachine":null,"hardwareModel":null,"hmsVersion":null,"isOpenPersonalRecommend":null,"isProgrammaticRecommend":null,"installedAppInfo":[{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.taobao.taobao","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.smile.gifmaker","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.ss.android.ugc.aweme.lite","firstInstallTime":null,"lastUpdateTime":null},{"appName":null,"appVersion":null,"isSystemApp":null,"packageName":"com.UCMobile","firstInstallTime":null,"lastUpdateTime":null}],"sysStartTime":null,"sysUpdateTime":null,"sysInitTime":null,"sysCompileTime":null,"updateMark":null,"bootMark":null,"appStoreVersion":null,"appstoreVersionCode":null,"caids":null,"localName":null,"cpuFreq":null,"hmsAgVersion":null,"aaid":null,"aaidMd5":null,"paid":null,"vendor":"HUAWEI","romVersion":null,"sysElapseTime":null,"sysUiVersion":null,"cookie":null,"referer":null,"isRoot":null,"skanVersion":null,"apiLevel":null},"network":{"ip":"************","ipv6":null,"mac":null,"macMd5":null,"ssid":null,"wifiMac":null,"connectType":null,"carrierType":null},"geo":null,"user":null,"pageInfos":null}
[2025-06-28 21:22:00.040] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server RTB , 570008194f634200bcc1f774ffe0c2e2 , 81
[2025-06-28 21:22:00.131] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{"36-16-30-63-SUCCESS_NON_PARTICIPATION":{"id":null,"statisticsType":"MINUTE","statisticsTime":"202506282120","mediaId":16,"mediaAppId":30,"mediaTagId":63,"code":"SUCCESS_NON_PARTICIPATION","total":1,"strategyId":36}},advertiserErrorCode:{}
[2025-06-28 21:22:00.132] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:22:04.429] [cn.taken.ad.task.DspRtbMonitorTask.executeDspEvent,165] INFO  - minute dsp_adv_event:{}
[2025-06-28 21:22:04.429] [cn.taken.ad.task.RtbEventMonitorTask.execute,185] INFO  - minute Event: mediaAdv:{},media:{},advertiser:{}
[2025-06-28 21:22:04.429] [cn.taken.ad.task.DspRtbMonitorTask.executeDspRequest,120] INFO  - minute dspAdvReq:{}
[2025-06-28 21:22:04.430] [cn.taken.ad.task.RtbMonitorTask.executeMediaAdvRequest,280] INFO  - minute Request: mediaAdvReq:{},minuteMediaReq:{},minuteAdvReq:{}
[2025-06-28 21:22:04.430] [cn.taken.ad.task.RtbMonitorTask.executeErrorCode,351] INFO  - minute ErrorCode mediaErrorCode:{},advertiserErrorCode:{}
[2025-06-28 21:22:08.152] [cn.taken.ad.api.RtbApi.rtb,314] INFO  - RtbId:17511167897840181,rtb mediaCode:6f0a4c40,appCode:1309260435081859072,tagCode:1309261134989561856,code:SUCCESS_NON_PARTICIPATION,msg:无填充
